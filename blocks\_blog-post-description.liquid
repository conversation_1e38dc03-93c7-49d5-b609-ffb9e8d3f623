{% liquid
  assign content = article.excerpt | default: article.content

  if block.settings.type_preset == 'rte' or block.settings.type_preset == 'paragraph'
    assign is_rte = true
  endif

  capture text_block_classes
    if block.settings.type_preset == 'custom'
      echo ' custom-typography '
      if block.settings.font_size != ''
        echo ' custom-font-size '
      endif
      if block.settings.color != ''
        echo ' custom-color '
      endif
    endif
    if is_rte
      echo ' rte '
    endif
  endcapture
%}

{% capture attributes %}
  class="blog-post-card__content-text spacing-style text-block text-block--{{ block.id }} {{ block.settings.type_preset }}
    {{ text_block_classes }}
  "

  style="
    {% render 'spacing-padding', settings: block.settings %}
    {% render 'typography-style', settings: block.settings %}
    {% if block.settings.color != '' %}
      --color: {{ block.settings.color }};
    {% endif %}
  "

  {{ block.shopify_attributes }}
{% endcapture %}

{% if content %}
  {% assign truncatewords = 30 %}
  {% unless article.image %}
    {% assign truncatewords = 90 %}
  {% endunless %}

  {% liquid
    assign element = 'div'
    if is_rte
      assign element = 'rte-formatter'
    endif
  %}

  <{{ element }} {{ attributes }}>
    {{ content | strip_html | truncatewords: truncatewords }}
    <a href="{{ article.url }}">{{ 'content.read_more' | t }}</a>
  </{{ element }}>
{% endif %}

{% stylesheet %}
  .blog-post-card__content-text a {
    color: var(--color-primary);
  }

  .custom-color,
  .custom-color > :is(h1, h2, h3, h4, h5, h6, p, *) {
    color: var(--color);
  }
{% endstylesheet %}

{% schema %}
{
  "name": "t:names.description",
  "settings": [
    {
      "type": "header",
      "content": "t:content.typography"
    },
    {
      "type": "select",
      "id": "type_preset",
      "label": "t:settings.preset",
      "options": [
        {
          "value": "rte",
          "label": "t:options.default"
        },
        {
          "value": "paragraph",
          "label": "t:options.paragraph"
        },
        {
          "value": "h1",
          "label": "t:options.h1"
        },
        {
          "value": "h2",
          "label": "t:options.h2"
        },
        {
          "value": "h3",
          "label": "t:options.h3"
        },
        {
          "value": "h4",
          "label": "t:options.h4"
        },
        {
          "value": "h5",
          "label": "t:options.h5"
        },
        {
          "value": "h6",
          "label": "t:options.h6"
        },
        {
          "value": "custom",
          "label": "t:options.custom"
        }
      ],
      "default": "rte",
      "info": "t:info.edit_presets_in_theme_settings"
    },
    {
      "type": "select",
      "id": "font",
      "label": "t:settings.font",
      "options": [
        {
          "value": "var(--font-body--family)",
          "label": "t:options.body"
        },
        {
          "value": "var(--font-subheading--family)",
          "label": "t:options.subheading"
        },
        {
          "value": "var(--font-heading--family)",
          "label": "t:options.heading"
        },
        {
          "value": "var(--font-accent--family)",
          "label": "t:options.accent"
        }
      ],
      "default": "var(--font-body--family)",
      "visible_if": "{{ block.settings.type_preset == 'custom' }}"
    },
    {
      "type": "select",
      "id": "font_size",
      "label": "t:settings.size",
      "options": [
        {
          "value": "",
          "label": "t:options.default"
        },
        {
          "value": "0.625rem",
          "label": "10px"
        },
        {
          "value": "0.75rem",
          "label": "12px"
        },
        {
          "value": "0.875rem",
          "label": "14px"
        },
        {
          "value": "1rem",
          "label": "16px"
        },
        {
          "value": "1.125rem",
          "label": "18px"
        },
        {
          "value": "1.25rem",
          "label": "20px"
        },
        {
          "value": "1.5rem",
          "label": "24px"
        },
        {
          "value": "2rem",
          "label": "32px"
        },
        {
          "value": "2.5rem",
          "label": "40px"
        },
        {
          "value": "3rem",
          "label": "48px"
        },
        {
          "value": "3.5rem",
          "label": "56px"
        },
        {
          "value": "4.5rem",
          "label": "72px"
        },
        {
          "value": "5.5rem",
          "label": "88px"
        },
        {
          "value": "7.5rem",
          "label": "120px"
        },
        {
          "value": "9.5rem",
          "label": "152px"
        },
        {
          "value": "11.5rem",
          "label": "184px"
        }
      ],
      "default": "1rem",
      "visible_if": "{{ block.settings.type_preset == 'custom' }}"
    },
    {
      "type": "select",
      "id": "line_height",
      "label": "t:settings.line_height",
      "options": [
        {
          "value": "tight",
          "label": "t:options.tight"
        },
        {
          "value": "normal",
          "label": "t:options.normal"
        },
        {
          "value": "loose",
          "label": "t:options.loose"
        }
      ],
      "default": "normal",
      "visible_if": "{{ block.settings.type_preset == 'custom' }}"
    },
    {
      "type": "select",
      "id": "letter_spacing",
      "label": "t:settings.letter_spacing",
      "options": [
        {
          "value": "tight",
          "label": "t:options.tight"
        },
        {
          "value": "normal",
          "label": "t:options.normal"
        },
        {
          "value": "loose",
          "label": "t:options.loose"
        }
      ],
      "default": "normal",
      "visible_if": "{{ block.settings.type_preset == 'custom' }}"
    },
    {
      "type": "select",
      "id": "case",
      "label": "t:settings.case",
      "options": [
        {
          "value": "none",
          "label": "t:options.default"
        },
        {
          "value": "uppercase",
          "label": "t:options.uppercase"
        }
      ],
      "default": "none",
      "visible_if": "{{ block.settings.type_preset == 'custom' }}"
    },
    {
      "type": "select",
      "id": "wrap",
      "label": "t:settings.wrap",
      "options": [
        {
          "value": "pretty",
          "label": "t:options.pretty"
        },
        {
          "value": "balance",
          "label": "t:options.balance"
        },
        {
          "value": "nowrap",
          "label": "t:options.none"
        }
      ],
      "visible_if": "{{ block.settings.type_preset == 'custom' }}"
    },
    {
      "type": "select",
      "id": "color",
      "label": "t:settings.color",
      "options": [
        {
          "value": "var(--color-foreground)",
          "label": "t:options.text"
        },
        {
          "value": "var(--color-foreground-heading)",
          "label": "t:options.heading"
        },
        {
          "value": "var(--color-primary)",
          "label": "t:options.link"
        }
      ],
      "default": "var(--color-foreground)",
      "visible_if": "{{ block.settings.type_preset != 'rte' }}"
    },
    {
      "type": "header",
      "content": "t:content.padding"
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "t:settings.top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 16
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "t:settings.bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "t:names.description"
    }
  ]
}
{% endschema %}
