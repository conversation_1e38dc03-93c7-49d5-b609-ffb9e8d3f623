{%- comment -%}
  Derives CSS variables from the menu typography settings for 2nd and 3rd level menu items.
  Accepts:
     settings: {block.settings}
{%- endcomment -%}

--menu-parent-font-family: var(--font-{{ settings.type_font_tertiary_link }}--family); --menu-parent-font-style:
var(--font-
{{- settings.type_font_tertiary_link -}}
--style); --menu-parent-font-weight: var(--font-
{{- settings.type_font_tertiary_link -}}
--weight); --menu-parent-font-case:
{% if settings.type_case_tertiary_link == 'uppercase' %}uppercase{% else %}none{% endif %};
{% case settings.menu_font_style %}
  {% when 'regular' %}
    --menu-parent-font-size: var(--menu-font-md--size); --menu-parent-font-line-height:
    var(--menu-font-md--line-height); --menu-parent-font-color: var(--color-foreground);
    --menu-parent-active-font-color: rgba(from var(--color-foreground) r g b / var(--opacity-subdued-text));
  {% when 'inverse' %}
    --menu-parent-font-size: var(--menu-font-sm--size); --menu-parent-font-line-height:
    var(--menu-font-sm--line-height); --menu-parent-font-color: rgba(from var(--color-foreground) r g b /
    var(--opacity-subdued-text)); --menu-parent-active-font-color: var(--color-foreground);
  {% when 'inverse_large' %}
    --menu-parent-font-size: var(--menu-font-sm--size); --menu-parent-font-line-height:
    var(--menu-font-sm--line-height); --menu-parent-font-color: rgba(from var(--color-foreground) r g b /
    var(--opacity-subdued-text)); --menu-parent-active-font-color: var(--color-foreground);
{% endcase %}
--menu-child-font-family: var(--font-{{ settings.type_font_secondary_link }}--family); --menu-child-font-style:
var(--font-
{{- settings.type_font_secondary_link -}}
--style); --menu-child-font-weight: var(--font-
{{- settings.type_font_secondary_link -}}
--weight); --menu-child-font-case:
{% if settings.type_case_secondary_link == 'uppercase' %}uppercase{% else %}none{% endif %};
{% case settings.menu_font_style %}
  {% when 'regular' %}
    --menu-child-font-size: var(--menu-font-sm--size); --menu-child-font-line-height: var(--menu-font-sm--line-height);
    --menu-child-font-color: rgba(from var(--color-foreground) r g b / var(--opacity-subdued-text));
    --menu-child-active-font-color: var(--color-foreground);
  {% when 'inverse' %}
    --menu-child-font-size: var(--menu-font-md--size); --menu-child-font-line-height: var(--menu-font-md--line-height);
    --menu-child-font-color: var(--color-foreground); --menu-child-active-font-color: rgba(from var(--color-foreground)
    r g b / var(--opacity-subdued-text));
  {% when 'inverse_large' %}
    --menu-child-font-size: var(--menu-font-xl--size); --menu-child-font-line-height: var(--menu-font-xl--line-height);
    --menu-child-font-color: var(--color-foreground); --menu-child-active-font-color: rgba(from var(--color-foreground)
    r g b / var(--opacity-subdued-text));
{% endcase %}
