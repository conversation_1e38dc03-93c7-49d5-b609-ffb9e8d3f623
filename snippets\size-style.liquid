{%- comment -%}
  Intended for blocks and sections that provide values for all the referenced settings.
  Accepts:
     settings: {block.settings || section.settings}
{%- endcomment -%}

{%- if settings.width == 'custom' -%}
  --size-style-width:
  {{- settings.custom_width }}%;
{%- elsif settings.width == 'fill' -%}
  --size-style-width: 100%;
{%- else -%}
  --size-style-width: {{ settings.width }};
{%- endif -%}

{%- if settings.height == 'custom' -%}
  --size-style-height:
  {{- settings.custom_height }}%;
{%- elsif settings.height == 'fill' -%}
  --size-style-height: 100%;
{%- else -%}
  --size-style-height: {{ settings.height }};
{%- endif -%}

{% if settings.width_mobile == 'custom' %}
  --size-style-width-mobile:
  {{- settings.custom_width_mobile }}%; --size-style-width-mobile-min:
  {{- settings.custom_width_mobile }}%;
{%- elsif settings.width_mobile == 'fill' -%}
  --size-style-width-mobile: 100%; --size-style-width-mobile-min: 5rem;
{%- elsif settings.width_mobile == 'fit-content' -%}
  --size-style-width-mobile: {{ settings.width_mobile }}; --size-style-width-mobile-min: {{ settings.width_mobile }};
{%- endif -%}
