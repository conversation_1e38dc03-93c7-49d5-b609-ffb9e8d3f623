{% # import schema from '../schemas/blocks/_collection-info.js' %}
<div
  class="collection-info collection-info--{{ block.settings.placement }} color-{{ block.settings.color_scheme }}"
  style="{% render 'spacing-style', settings: block.settings %} {% if block.settings.icons_style contains 'large' %} --slideshow-icon-padding: 0;{% endif %}"
  {{ block.shopify_attributes }}
>
  <div class="collection-info__content">
    {% content_for 'blocks' %}
  </div>

  <div class="collection-info__controls collection-info__controls--{{ block.settings.navigation }}">
    {%- render 'slideshow-controls', icon_style: block.settings.icons_style, shape: block.settings.background_style -%}
  </div>
</div>

{% schema %}
{
  "name": "t:names.collection_info",
  "tag": null,
  "blocks": [
    {
      "type": "@theme"
    },
    {
      "type": "@app"
    }
  ],
  "settings": [
    {
      "type": "select",
      "id": "placement",
      "label": "t:settings.placement",
      "options": [
        {
          "label": "t:options.above_carousel",
          "value": "above-carousel"
        },
        {
          "label": "t:options.next_to_carousel",
          "value": "next-to-carousel"
        }
      ]
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:settings.color_scheme"
    },
    {
      "type": "header",
      "content": "t:content.arrows"
    },
    {
      "type": "select",
      "id": "navigation",
      "label": "t:settings.navigation",
      "options": [
        {
          "label": "t:options.above_carousel",
          "value": "above-carousel"
        },
        {
          "label": "t:options.inside_carousel",
          "value": "inside-carousel"
        }
      ],
      "visible_if": "{{ block.settings.placement == 'above-carousel' }}"
    },
    {
      "type": "select",
      "id": "icons_style",
      "label": "t:settings.icons",
      "options": [
        {
          "value": "arrows",
          "label": "t:options.arrows"
        },
        {
          "value": "chevron",
          "label": "t:options.chevrons"
        },
        {
          "value": "arrows_large",
          "label": "t:options.large_arrows"
        },
        {
          "value": "chevron_large",
          "label": "t:options.large_chevrons"
        },
        {
          "value": "none",
          "label": "t:options.none"
        }
      ]
    },
    {
      "type": "select",
      "id": "background_style",
      "label": "t:settings.background",
      "options": [
        {
          "value": "none",
          "label": "t:options.none"
        },
        {
          "value": "circle",
          "label": "t:options.circle"
        },
        {
          "value": "square",
          "label": "t:options.square"
        }
      ]
    },
    {
      "type": "header",
      "content": "t:content.padding"
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "t:settings.top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "t:settings.bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-inline-start",
      "label": "t:settings.left",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-inline-end",
      "label": "t:settings.right",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "t:names.collection_info"
    }
  ]
}
{% endschema %}
