/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "layout": "password",
  "sections": {
    "main": {
      "type": "password",
      "blocks": {
        "logo": {
          "type": "logo",
          "name": "Logo",
          "settings": {
            "inverse": false,
            "font": "heading",
            "unit": "pixel",
            "percent_width": 100,
            "pixel_height": 24,
            "custom_mobile_size": false,
            "unit_mobile": "percent",
            "percent_width_mobile": 80,
            "pixel_height_mobile": 120,
            "padding-block-start": 0,
            "padding-block-end": 32,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {}
        },
        "text": {
          "type": "text",
          "name": "Text",
          "settings": {
            "text": "<h1>Opening soon</h1>",
            "width": "fit-content",
            "max_width": "normal",
            "alignment": "left",
            "type_preset": "h3",
            "font": "var(--font-primary--family)",
            "font_size": "",
            "line_height": "normal",
            "letter_spacing": "normal",
            "case": "none",
            "wrap": "pretty",
            "color": "",
            "background": false,
            "background_color": "#00000026",
            "corner_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {}
        },
        "text-2": {
          "type": "text",
          "name": "Text",
          "settings": {
            "text": "<p>Sign up for our newsletter to be the first to know when we launch.</p>",
            "width": "fit-content",
            "max_width": "normal",
            "alignment": "left",
            "type_preset": "rte",
            "font": "var(--font-primary--family)",
            "font_size": "",
            "line_height": "normal",
            "letter_spacing": "normal",
            "case": "none",
            "wrap": "pretty",
            "color": "",
            "background": false,
            "background_color": "#00000026",
            "corner_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 32,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {}
        },
        "email-signup": {
          "type": "email-signup",
          "name": "Email Signup",
          "settings": {
            "width": "custom",
            "custom_width": 50,
            "inherit_color_scheme": true,
            "color_scheme": "",
            "border_style": "all",
            "border_width": 1,
            "border_radius": 14,
            "input_type_preset": "paragraph",
            "style_class": "button",
            "display_type": "text",
            "label": "Sign up",
            "integrated_button": false,
            "button_type_preset": "paragraph",
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {}
        }
      },
      "block_order": [
        "logo",
        "text",
        "text-2",
        "email-signup"
      ],
      "name": "Password",
      "settings": {
        "content_direction": "column",
        "vertical_on_mobile": true,
        "horizontal_alignment": "center",
        "vertical_alignment": "center",
        "align_baseline": false,
        "horizontal_alignment_flex_direction_column": "center",
        "vertical_alignment_flex_direction_column": "center",
        "gap": 12,
        "section_width": "page-width",
        "color_scheme": "",
        "background_media": "none",
        "video_position": "cover",
        "background_image_position": "cover",
        "border": "none",
        "border_width": 1,
        "border_opacity": 100,
        "border_radius": 0,
        "padding-block-start": 100,
        "padding-block-end": 100
      }
    }
  },
  "order": [
    "main"
  ]
}
