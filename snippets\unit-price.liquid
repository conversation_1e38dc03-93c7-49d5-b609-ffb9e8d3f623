{%- doc -%}
  Renders the unit price, including its measurement.

  @param {object} price - The unit price (money or string).
  @param {object} measurement - The unit_price_measurement object.

  @example
  {% render 'unit-price', price: variant.unit_price, measurement: variant.unit_price_measurement %}

  @example
  {% render 'unit-price', price: line_item.unit_price | money_with_currency, measurement: line_item.unit_price_measurement }
{%- enddoc -%}
<small class="unit-price">
  <span class="visually-hidden">{{ 'accessibility.unit_price' | t }}</span>
  {{ price | unit_price_with_measurement: measurement }}
</small>
