{% # import schema from '../schemas/blocks/_footer-copyright.js' %}

<div
  class="
    footer-utilities__group-copyright
    custom-typography
    {% if block.settings.font_size != "" %}custom-font-size{% endif %}
  "
  style="{% render 'typography-style', preset: 'custom', settings: block.settings %}"
  {{ block.shopify_attributes }}
>
  <span class="footer-utilities__text">
    &copy; {{ 'now' | date: '%Y' }}
    {{ shop.name | link_to: routes.root_url -}}
    , {{ powered_by_link }}
  </span>
</div>

{% schema %}
{
  "name": "t:names.copyright",
  "tag": null,
  "settings": [
    {
      "type": "paragraph",
      "content": "t:content.copyright_info"
    },
    {
      "type": "select",
      "id": "font_size",
      "label": "t:settings.size",
      "options": [
        {
          "value": "0.625rem",
          "label": "10px"
        },
        {
          "value": "0.75rem",
          "label": "12px"
        },
        {
          "value": "0.875rem",
          "label": "14px"
        },
        {
          "value": "1rem",
          "label": "16px"
        },
        {
          "value": "1.125rem",
          "label": "18px"
        }
      ],
      "default": "0.75rem"
    },
    {
      "type": "select",
      "id": "case",
      "label": "t:settings.case",
      "options": [
        {
          "value": "none",
          "label": "t:options.default"
        },
        {
          "value": "uppercase",
          "label": "t:options.uppercase"
        }
      ],
      "default": "none"
    }
  ]
}
{% endschema %}
