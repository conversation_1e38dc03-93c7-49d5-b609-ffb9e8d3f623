[{"name": "theme_info", "theme_name": "Horizon", "theme_version": "2.0.0", "theme_author": "Shopify", "theme_documentation_url": "https://help.shopify.com/manual/online-store/themes", "theme_support_url": "https://support.shopify.com/"}, {"name": "t:names.logo_and_favicon", "settings": [{"type": "paragraph", "content": "t:content.manage_store_name"}, {"type": "image_picker", "id": "logo", "label": "t:settings.default_logo"}, {"type": "image_picker", "id": "logo_inverse", "label": "t:settings.inverse_logo", "info": "t:content.inverse_logo_info"}, {"type": "image_picker", "id": "favicon", "label": "t:settings.favicon"}]}, {"name": "t:names.colors", "settings": [{"type": "color_scheme_group", "id": "color_schemes", "definition": [{"type": "color", "id": "background", "label": "t:settings.background", "default": "#FFFFFF", "alpha": true}, {"type": "color", "id": "foreground_heading", "label": "t:settings.headings", "default": "#000000", "alpha": true}, {"type": "color", "id": "foreground", "label": "t:settings.text", "default": "#000000", "alpha": true}, {"type": "color", "id": "primary", "label": "t:settings.primary_color", "default": "#000F9F", "alpha": true}, {"type": "color", "id": "primary_hover", "label": "t:settings.primary_hover_color", "default": "#000000", "alpha": true}, {"type": "color", "id": "border", "label": "t:settings.borders", "default": "#E6E6E6", "alpha": true}, {"type": "color", "id": "shadow", "label": "t:settings.shadow_color", "default": "#000000", "alpha": true}, {"type": "header", "content": "t:names.primary_button"}, {"type": "color", "id": "primary_button_background", "label": "t:settings.background", "default": "#000F9F", "alpha": true}, {"type": "color", "id": "primary_button_text", "label": "t:settings.text", "default": "#FFFFFF", "alpha": true}, {"type": "color", "id": "primary_button_border", "label": "t:settings.borders", "default": "#000F9F", "alpha": true}, {"type": "color", "id": "primary_button_hover_background", "label": "t:settings.hover_background", "default": "#000F9F", "alpha": true}, {"type": "color", "id": "primary_button_hover_text", "label": "t:settings.hover_text", "default": "#FFFFFF", "alpha": true}, {"type": "color", "id": "primary_button_hover_border", "label": "t:settings.hover_borders", "default": "#000F9F", "alpha": true}, {"type": "header", "content": "t:names.secondary_button"}, {"type": "color", "id": "secondary_button_background", "label": "t:settings.background", "default": "#FFFFFF", "alpha": true}, {"type": "color", "id": "secondary_button_text", "label": "t:settings.text", "default": "#000000", "alpha": true}, {"type": "color", "id": "secondary_button_border", "label": "t:settings.borders", "default": "#000000", "alpha": true}, {"type": "color", "id": "secondary_button_hover_background", "label": "t:settings.hover_background", "default": "#FFFFFF", "alpha": true}, {"type": "color", "id": "secondary_button_hover_text", "label": "t:settings.hover_text", "default": "#000000", "alpha": true}, {"type": "color", "id": "secondary_button_hover_border", "label": "t:settings.hover_borders", "default": "#000000", "alpha": true}, {"type": "header", "content": "t:names.inputs"}, {"type": "color", "id": "input_background", "label": "t:settings.background", "default": "#FFFFFF", "alpha": true}, {"type": "color", "id": "input_text_color", "label": "t:settings.text", "default": "#000000", "alpha": true}, {"type": "color", "id": "input_border_color", "label": "t:settings.borders", "default": "#000000", "alpha": true}, {"type": "color", "id": "input_hover_background", "label": "t:settings.hover_background", "default": "#F5F5F5", "alpha": true}, {"type": "header", "content": "t:names.variants"}, {"type": "color", "id": "variant_background_color", "label": "t:settings.background", "default": "#FFFFFF", "alpha": true}, {"type": "color", "id": "variant_text_color", "label": "t:settings.text", "default": "#000000", "alpha": true}, {"type": "color", "id": "variant_border_color", "label": "t:settings.borders", "default": "#E6E6E6", "alpha": true}, {"type": "color", "id": "variant_hover_background_color", "label": "t:settings.hover_background", "default": "#F5F5F5", "alpha": true}, {"type": "color", "id": "variant_hover_text_color", "label": "t:settings.hover_text", "default": "#000000", "alpha": true}, {"type": "color", "id": "variant_hover_border_color", "label": "t:settings.hover_borders", "default": "#E6E6E6", "alpha": true}, {"type": "header", "content": "t:names.selected_variants"}, {"type": "color", "id": "selected_variant_background_color", "label": "t:settings.background", "default": "#000000", "alpha": true}, {"type": "color", "id": "selected_variant_text_color", "label": "t:settings.text", "default": "#FFFFFF", "alpha": true}, {"type": "color", "id": "selected_variant_border_color", "label": "t:settings.borders", "default": "#000000", "alpha": true}, {"type": "color", "id": "selected_variant_hover_background_color", "label": "t:settings.hover_background", "default": "#1A1A1A", "alpha": true}, {"type": "color", "id": "selected_variant_hover_text_color", "label": "t:settings.hover_text", "default": "#FFFFFF", "alpha": true}, {"type": "color", "id": "selected_variant_hover_border_color", "label": "t:settings.hover_borders", "default": "#1A1A1A", "alpha": true}], "role": {"text": "foreground", "background": "background", "links": "primary", "icons": "foreground", "primary_button": "primary_button_background", "on_primary_button": "primary_button_text", "primary_button_border": "primary_button_border", "secondary_button": "secondary_button_background", "on_secondary_button": "secondary_button_text", "secondary_button_border": "secondary_button_border"}}]}, {"name": "t:names.typography", "settings": [{"type": "header", "content": "t:content.fonts"}, {"type": "font_picker", "id": "type_body_font", "default": "work_sans_n4", "label": "t:options.body"}, {"type": "font_picker", "id": "type_subheading_font", "default": "work_sans_n5", "label": "t:options.subheading"}, {"type": "font_picker", "id": "type_heading_font", "default": "anonymous_pro_n4", "label": "t:options.heading"}, {"type": "font_picker", "id": "type_accent_font", "default": "anonymous_pro_n4", "label": "t:options.accent"}, {"type": "header", "content": "t:content.text_presets"}, {"type": "paragraph", "content": "t:content.responsive_font_sizes"}, {"type": "header", "content": "t:content.paragraph"}, {"type": "select", "id": "type_size_paragraph", "label": "t:settings.size", "options": [{"value": "10", "label": "10px"}, {"value": "12", "label": "12px"}, {"value": "14", "label": "14px"}, {"value": "16", "label": "16px"}, {"value": "18", "label": "18px"}], "default": "14"}, {"type": "select", "id": "type_line_height_paragraph", "label": "t:settings.line_height", "options": [{"value": "body-tight", "label": "t:options.tight"}, {"value": "body-normal", "label": "t:options.normal"}, {"value": "body-loose", "label": "t:options.loose"}], "default": "body-normal"}, {"type": "header", "content": "t:options.h1"}, {"type": "select", "id": "type_font_h1", "label": "t:settings.font", "options": [{"value": "heading", "label": "t:options.heading"}, {"value": "accent", "label": "t:options.accent"}], "default": "heading"}, {"type": "select", "id": "type_size_h1", "label": "t:settings.size", "options": [{"value": "10", "label": "10px"}, {"value": "12", "label": "12px"}, {"value": "14", "label": "14px"}, {"value": "16", "label": "16px"}, {"value": "18", "label": "18px"}, {"value": "20", "label": "20px"}, {"value": "24", "label": "24px"}, {"value": "32", "label": "32px"}, {"value": "40", "label": "40px"}, {"value": "48", "label": "48px"}, {"value": "56", "label": "56px"}, {"value": "72", "label": "72px"}, {"value": "88", "label": "88px"}, {"value": "120", "label": "120px"}, {"value": "152", "label": "152px"}, {"value": "184", "label": "184px"}], "default": "72"}, {"type": "select", "id": "type_line_height_h1", "label": "t:settings.line_height", "options": [{"value": "display-tight", "label": "t:options.tight"}, {"value": "display-normal", "label": "t:options.normal"}, {"value": "display-loose", "label": "t:options.loose"}], "default": "display-normal"}, {"type": "select", "id": "type_letter_spacing_h1", "label": "t:settings.letter_spacing", "options": [{"value": "heading-tight", "label": "t:options.tight"}, {"value": "heading-normal", "label": "t:options.normal"}, {"value": "heading-loose", "label": "t:options.loose"}], "default": "heading-normal"}, {"type": "select", "id": "type_case_h1", "label": "t:settings.text_case", "options": [{"value": "none", "label": "t:options.default"}, {"value": "uppercase", "label": "t:options.uppercase"}], "default": "none"}, {"type": "header", "content": "t:options.h2"}, {"type": "select", "id": "type_font_h2", "label": "t:settings.font", "options": [{"value": "heading", "label": "t:options.heading"}, {"value": "accent", "label": "t:options.accent"}], "default": "heading"}, {"type": "select", "id": "type_size_h2", "label": "t:settings.size", "options": [{"value": "10", "label": "10px"}, {"value": "12", "label": "12px"}, {"value": "14", "label": "14px"}, {"value": "16", "label": "16px"}, {"value": "18", "label": "18px"}, {"value": "20", "label": "20px"}, {"value": "24", "label": "24px"}, {"value": "32", "label": "32px"}, {"value": "40", "label": "40px"}, {"value": "48", "label": "48px"}, {"value": "56", "label": "56px"}, {"value": "72", "label": "72px"}, {"value": "88", "label": "88px"}, {"value": "120", "label": "120px"}, {"value": "152", "label": "152px"}, {"value": "184", "label": "184px"}], "default": "48"}, {"type": "select", "id": "type_line_height_h2", "label": "t:settings.line_height", "options": [{"value": "display-tight", "label": "t:options.tight"}, {"value": "display-normal", "label": "t:options.normal"}, {"value": "display-loose", "label": "t:options.loose"}], "default": "display-normal"}, {"type": "select", "id": "type_letter_spacing_h2", "label": "t:settings.letter_spacing", "options": [{"value": "heading-tight", "label": "t:options.tight"}, {"value": "heading-normal", "label": "t:options.normal"}, {"value": "heading-loose", "label": "t:options.loose"}], "default": "heading-normal"}, {"type": "select", "id": "type_case_h2", "label": "t:settings.text_case", "options": [{"value": "none", "label": "t:options.default"}, {"value": "uppercase", "label": "t:options.uppercase"}], "default": "none"}, {"type": "header", "content": "t:options.h3"}, {"type": "select", "id": "type_font_h3", "label": "t:settings.font", "options": [{"value": "heading", "label": "t:options.heading"}, {"value": "accent", "label": "t:options.accent"}, {"value": "subheading", "label": "t:options.subheading"}, {"value": "body", "label": "t:options.body"}], "default": "heading"}, {"type": "select", "id": "type_size_h3", "label": "t:settings.size", "options": [{"value": "10", "label": "10px"}, {"value": "12", "label": "12px"}, {"value": "14", "label": "14px"}, {"value": "16", "label": "16px"}, {"value": "18", "label": "18px"}, {"value": "20", "label": "20px"}, {"value": "24", "label": "24px"}, {"value": "32", "label": "32px"}, {"value": "40", "label": "40px"}, {"value": "48", "label": "48px"}, {"value": "56", "label": "56px"}, {"value": "72", "label": "72px"}, {"value": "88", "label": "88px"}, {"value": "120", "label": "120px"}, {"value": "152", "label": "152px"}, {"value": "184", "label": "184px"}], "default": "32"}, {"type": "select", "id": "type_line_height_h3", "label": "t:settings.line_height", "options": [{"value": "display-tight", "label": "t:options.tight"}, {"value": "display-normal", "label": "t:options.normal"}, {"value": "display-loose", "label": "t:options.loose"}], "default": "display-normal"}, {"type": "select", "id": "type_letter_spacing_h3", "label": "t:settings.letter_spacing", "options": [{"value": "heading-tight", "label": "t:options.tight"}, {"value": "heading-normal", "label": "t:options.normal"}, {"value": "heading-loose", "label": "t:options.loose"}], "default": "heading-normal"}, {"type": "select", "id": "type_case_h3", "label": "t:settings.text_case", "options": [{"value": "none", "label": "t:options.default"}, {"value": "uppercase", "label": "t:options.uppercase"}], "default": "none"}, {"type": "header", "content": "t:options.h4"}, {"type": "select", "id": "type_font_h4", "label": "t:settings.font", "options": [{"value": "heading", "label": "t:options.heading"}, {"value": "accent", "label": "t:options.accent"}, {"value": "subheading", "label": "t:options.subheading"}, {"value": "body", "label": "t:options.body"}], "default": "subheading"}, {"type": "select", "id": "type_size_h4", "label": "t:settings.size", "options": [{"value": "10", "label": "10px"}, {"value": "12", "label": "12px"}, {"value": "14", "label": "14px"}, {"value": "16", "label": "16px"}, {"value": "18", "label": "18px"}, {"value": "20", "label": "20px"}, {"value": "24", "label": "24px"}, {"value": "32", "label": "32px"}, {"value": "40", "label": "40px"}, {"value": "48", "label": "48px"}, {"value": "56", "label": "56px"}, {"value": "72", "label": "72px"}, {"value": "88", "label": "88px"}, {"value": "120", "label": "120px"}, {"value": "152", "label": "152px"}, {"value": "184", "label": "184px"}], "default": "24"}, {"type": "select", "id": "type_line_height_h4", "label": "t:settings.line_height", "options": [{"value": "display-tight", "label": "t:options.tight"}, {"value": "display-normal", "label": "t:options.normal"}, {"value": "display-loose", "label": "t:options.loose"}], "default": "display-normal"}, {"type": "select", "id": "type_letter_spacing_h4", "label": "t:settings.letter_spacing", "options": [{"value": "heading-tight", "label": "t:options.tight"}, {"value": "heading-normal", "label": "t:options.normal"}, {"value": "heading-loose", "label": "t:options.loose"}], "default": "heading-normal"}, {"type": "select", "id": "type_case_h4", "label": "t:settings.text_case", "options": [{"value": "none", "label": "t:options.default"}, {"value": "uppercase", "label": "t:options.uppercase"}], "default": "none"}, {"type": "header", "content": "t:options.h5"}, {"type": "select", "id": "type_font_h5", "label": "t:settings.font", "options": [{"value": "heading", "label": "t:options.heading"}, {"value": "accent", "label": "t:options.accent"}, {"value": "subheading", "label": "t:options.subheading"}, {"value": "body", "label": "t:options.body"}], "default": "subheading"}, {"type": "select", "id": "type_size_h5", "label": "t:settings.size", "options": [{"value": "10", "label": "10px"}, {"value": "12", "label": "12px"}, {"value": "14", "label": "14px"}, {"value": "16", "label": "16px"}, {"value": "18", "label": "18px"}, {"value": "20", "label": "20px"}, {"value": "24", "label": "24px"}, {"value": "32", "label": "32px"}, {"value": "40", "label": "40px"}, {"value": "48", "label": "48px"}, {"value": "56", "label": "56px"}, {"value": "72", "label": "72px"}, {"value": "88", "label": "88px"}, {"value": "120", "label": "120px"}, {"value": "152", "label": "152px"}, {"value": "184", "label": "184px"}], "default": "18"}, {"type": "select", "id": "type_line_height_h5", "label": "t:settings.line_height", "options": [{"value": "display-tight", "label": "t:options.tight"}, {"value": "display-normal", "label": "t:options.normal"}, {"value": "display-loose", "label": "t:options.loose"}], "default": "display-normal"}, {"type": "select", "id": "type_letter_spacing_h5", "label": "t:settings.letter_spacing", "options": [{"value": "heading-tight", "label": "t:options.tight"}, {"value": "heading-normal", "label": "t:options.normal"}, {"value": "heading-loose", "label": "t:options.loose"}], "default": "heading-normal"}, {"type": "select", "id": "type_case_h5", "label": "t:settings.text_case", "options": [{"value": "none", "label": "t:options.default"}, {"value": "uppercase", "label": "t:options.uppercase"}], "default": "none"}, {"type": "header", "content": "t:options.h6"}, {"type": "select", "id": "type_font_h6", "label": "t:settings.font", "options": [{"value": "heading", "label": "t:options.heading"}, {"value": "accent", "label": "t:options.accent"}, {"value": "subheading", "label": "t:options.subheading"}, {"value": "body", "label": "t:options.body"}], "default": "subheading"}, {"type": "select", "id": "type_size_h6", "label": "t:settings.size", "options": [{"value": "10", "label": "10px"}, {"value": "12", "label": "12px"}, {"value": "14", "label": "14px"}, {"value": "16", "label": "16px"}, {"value": "18", "label": "18px"}, {"value": "20", "label": "20px"}, {"value": "24", "label": "24px"}, {"value": "32", "label": "32px"}, {"value": "40", "label": "40px"}, {"value": "48", "label": "48px"}, {"value": "56", "label": "56px"}, {"value": "72", "label": "72px"}, {"value": "88", "label": "88px"}, {"value": "120", "label": "120px"}, {"value": "152", "label": "152px"}, {"value": "184", "label": "184px"}], "default": "16"}, {"type": "select", "id": "type_line_height_h6", "label": "t:settings.line_height", "options": [{"value": "display-tight", "label": "t:options.tight"}, {"value": "display-normal", "label": "t:options.normal"}, {"value": "display-loose", "label": "t:options.loose"}], "default": "display-normal"}, {"type": "select", "id": "type_letter_spacing_h6", "label": "t:settings.letter_spacing", "options": [{"value": "heading-tight", "label": "t:options.tight"}, {"value": "heading-normal", "label": "t:options.normal"}, {"value": "heading-loose", "label": "t:options.loose"}], "default": "heading-normal"}, {"type": "select", "id": "type_case_h6", "label": "t:settings.text_case", "options": [{"value": "none", "label": "t:options.default"}, {"value": "uppercase", "label": "t:options.uppercase"}], "default": "none"}]}, {"name": "t:names.page_layout", "settings": [{"type": "select", "id": "page_width", "label": "t:settings.page_width", "options": [{"value": "narrow", "label": "t:options.narrow"}, {"value": "normal", "label": "t:options.normal"}, {"value": "wide", "label": "t:options.wide"}], "default": "narrow"}]}, {"name": "t:names.animations", "settings": [{"type": "checkbox", "id": "page_transition_enabled", "label": "t:settings.page_transition_enabled", "default": true}, {"type": "checkbox", "id": "transition_to_main_product", "label": "t:settings.transition_to_main_product", "default": true}, {"type": "select", "id": "card_hover_effect", "label": "t:settings.card_hover_effect", "options": [{"value": "none", "label": "t:options.none"}, {"value": "lift", "label": "t:options.lift"}, {"value": "scale", "label": "t:options.scale"}, {"value": "subtle-zoom", "label": "t:options.subtle_zoom"}], "default": "lift", "info": "t:info.hover_effects"}]}, {"name": "t:names.badges", "settings": [{"type": "select", "id": "badge_position", "options": [{"value": "bottom-left", "label": "t:options.bottom_left"}, {"value": "top-left", "label": "t:options.top_left"}, {"value": "top-right", "label": "t:options.top_right"}], "default": "top-left", "label": "t:settings.badge_position"}, {"type": "range", "id": "badge_corner_radius", "min": 0, "max": 100, "step": 2, "unit": "px", "label": "t:settings.badge_corner_radius", "default": 40}, {"type": "header", "content": "t:settings.colors"}, {"type": "color_scheme", "id": "badge_sale_color_scheme", "label": "t:settings.badge_sale_color_scheme", "default": "scheme-4"}, {"type": "color_scheme", "id": "badge_sold_out_color_scheme", "label": "t:settings.badge_sold_out_color_scheme", "default": "scheme-5"}, {"type": "header", "content": "t:names.typography"}, {"type": "select", "id": "badge_font_family", "label": "t:settings.font", "options": [{"value": "body", "label": "t:options.body"}, {"value": "subheading", "label": "t:options.subheading"}, {"value": "heading", "label": "t:options.heading"}, {"value": "accent", "label": "t:options.accent"}], "default": "body"}, {"type": "select", "id": "badge_text_transform", "label": "t:settings.text_case", "options": [{"value": "none", "label": "t:options.default"}, {"value": "uppercase", "label": "t:options.uppercase"}], "default": "none"}]}, {"name": "t:names.buttons", "settings": [{"type": "header", "content": "t:options.button_primary"}, {"type": "range", "id": "primary_button_border_width", "min": 0, "max": 4, "step": 1, "unit": "px", "label": "t:settings.border_width", "default": 0}, {"type": "range", "id": "button_border_radius_primary", "min": 0, "max": 100, "step": 1, "unit": "px", "label": "t:settings.border_radius", "default": 100}, {"type": "select", "id": "type_font_button_primary", "label": "t:settings.font", "options": [{"value": "body", "label": "t:options.body"}, {"value": "accent", "label": "t:options.accent"}], "default": "body"}, {"type": "select", "id": "button_font_weight_primary", "label": "t:settings.button_text_weight", "options": [{"value": "default", "label": "t:options.default"}, {"value": "100", "label": "t:options.thin"}, {"value": "300", "label": "t:options.light"}, {"value": "400", "label": "t:options.regular"}, {"value": "500", "label": "t:options.medium"}, {"value": "600", "label": "t:options.semibold"}, {"value": "700", "label": "t:options.bold"}, {"value": "900", "label": "t:options.black"}], "default": "default"}, {"type": "select", "id": "button_text_case_primary", "label": "t:settings.button_text_case", "options": [{"value": "default", "label": "t:options.default"}, {"value": "uppercase", "label": "t:options.uppercase"}], "default": "default"}, {"type": "header", "content": "t:options.button_secondary"}, {"type": "range", "id": "secondary_button_border_width", "min": 0, "max": 4, "step": 1, "unit": "px", "label": "t:settings.border_width", "default": 1}, {"type": "range", "id": "button_border_radius_secondary", "min": 0, "max": 100, "step": 1, "unit": "px", "label": "t:settings.border_radius", "default": 100}, {"type": "select", "id": "type_font_button_secondary", "label": "t:settings.font", "options": [{"value": "body", "label": "t:options.body"}, {"value": "accent", "label": "t:options.accent"}], "default": "body"}, {"type": "select", "id": "button_font_weight_secondary", "label": "t:settings.button_text_weight", "options": [{"value": "default", "label": "t:options.default"}, {"value": "100", "label": "t:options.thin"}, {"value": "300", "label": "t:options.light"}, {"value": "400", "label": "t:options.regular"}, {"value": "500", "label": "t:options.medium"}, {"value": "600", "label": "t:options.semibold"}, {"value": "700", "label": "t:options.bold"}, {"value": "900", "label": "t:options.black"}], "default": "default"}, {"type": "select", "id": "button_text_case_secondary", "label": "t:settings.button_text_case", "options": [{"value": "default", "label": "t:options.default"}, {"value": "uppercase", "label": "t:options.uppercase"}], "default": "default"}, {"type": "header", "content": "t:names.pills"}, {"type": "paragraph", "content": "t:info.pills_usage"}, {"type": "range", "id": "pills_border_radius", "label": "t:settings.border_radius", "min": 0, "max": 40, "step": 1, "unit": "px", "default": 40}]}, {"name": "t:names.cart", "settings": [{"type": "select", "id": "cart_type", "label": "t:settings.cart_type", "options": [{"value": "page", "label": "t:options.page"}, {"value": "drawer", "label": "t:options.drawer"}], "default": "page"}, {"type": "select", "id": "product_title_case", "label": "t:settings.product_title_case", "options": [{"value": "default", "label": "t:options.default"}, {"value": "uppercase", "label": "t:options.uppercase"}], "default": "default"}, {"type": "select", "id": "cart_price_font", "label": "t:settings.font_price", "options": [{"value": "primary", "label": "t:options.body"}, {"value": "secondary", "label": "t:options.subheading"}, {"value": "tertiary", "label": "t:options.tertiary"}], "default": "secondary"}, {"type": "checkbox", "id": "auto_open_cart_drawer", "label": "t:settings.auto_open_cart_drawer", "default": false, "visible_if": "{{ settings.cart_type == 'drawer' }}"}, {"type": "checkbox", "id": "show_cart_note", "label": "t:settings.seller_note", "default": false}, {"type": "checkbox", "id": "show_add_discount_code", "label": "t:settings.add_discount_code", "default": true}, {"type": "checkbox", "id": "show_installments", "label": "t:settings.installments", "default": true}, {"type": "checkbox", "id": "show_accelerated_checkout_buttons", "label": "t:settings.checkout_buttons", "info": "t:info.checkout_buttons", "default": true}]}, {"name": "t:names.drawers", "settings": [{"type": "color_scheme", "id": "drawer_color_scheme", "label": "t:settings.color_scheme", "default": "scheme-1"}, {"type": "select", "id": "drawer_border", "label": "t:settings.borders", "options": [{"value": "none", "label": "t:options.none"}, {"value": "solid", "label": "t:options.solid"}], "default": "none"}, {"type": "range", "id": "drawer_border_width", "min": 0, "max": 10, "step": 1, "unit": "px", "label": "t:settings.border_width", "default": 1, "visible_if": "{{ settings.drawer_border != 'none' }}"}, {"type": "range", "id": "drawer_border_opacity", "min": 0, "max": 100, "step": 1, "unit": "%", "label": "t:settings.border_opacity", "default": 50, "visible_if": "{{ settings.drawer_border != 'none' }}"}, {"type": "checkbox", "id": "drawer_drop_shadow", "label": "t:settings.drop_shadow", "default": false}]}, {"name": "t:names.icons", "settings": [{"type": "select", "id": "icon_stroke", "label": "t:settings.stroke", "options": [{"value": "thin", "label": "t:options.thin"}, {"value": "default", "label": "t:options.default"}, {"value": "heavy", "label": "t:options.heavy"}], "default": "default"}]}, {"name": "t:names.input_fields", "settings": [{"type": "range", "id": "input_border_width", "min": 0, "max": 4, "step": 1, "unit": "px", "label": "t:settings.border_width", "default": 1}, {"type": "range", "id": "inputs_border_radius", "min": 0, "max": 32, "step": 1, "unit": "px", "label": "t:settings.border_radius", "default": 8}, {"type": "select", "id": "type_preset", "label": "t:settings.type_preset", "options": [{"value": "", "label": "t:options.default"}, {"value": "paragraph", "label": "t:options.paragraph"}, {"value": "h1", "label": "t:options.h1"}, {"value": "h2", "label": "t:options.h2"}, {"value": "h3", "label": "t:options.h3"}, {"value": "h4", "label": "t:options.h4"}, {"value": "h5", "label": "t:options.h5"}, {"value": "h6", "label": "t:options.h6"}], "default": "paragraph"}]}, {"name": "t:names.popovers", "settings": [{"type": "color_scheme", "id": "popover_color_scheme", "label": "t:settings.color_scheme", "default": "scheme-1"}, {"type": "range", "id": "popover_border_radius", "label": "t:settings.border_radius", "min": 0, "max": 16, "step": 1, "unit": "px", "default": 8}, {"type": "select", "id": "popover_border", "label": "t:settings.borders", "options": [{"value": "none", "label": "t:options.none"}, {"value": "solid", "label": "t:options.solid"}], "default": "solid"}, {"type": "range", "id": "popover_border_width", "min": 0, "max": 10, "step": 1, "unit": "px", "label": "t:settings.border_width", "default": 1, "visible_if": "{{ settings.popover_border != 'none' }}"}, {"type": "range", "id": "popover_border_opacity", "min": 0, "max": 100, "step": 1, "unit": "%", "label": "t:settings.border_opacity", "default": 50, "visible_if": "{{ settings.popover_border != 'none' }}"}, {"type": "checkbox", "id": "popover_drop_shadow", "label": "t:settings.drop_shadow", "default": true}]}, {"name": "t:names.prices", "settings": [{"type": "header", "content": "t:settings.currency_code"}, {"type": "checkbox", "id": "currency_code_enabled_product_pages", "label": "t:settings.product_pages", "default": true}, {"type": "checkbox", "id": "currency_code_enabled_product_cards", "label": "t:settings.product_cards", "default": true}, {"type": "checkbox", "id": "currency_code_enabled_cart_items", "label": "t:settings.cart_items", "default": true}, {"type": "checkbox", "id": "currency_code_enabled_cart_total", "label": "t:settings.cart_total", "default": true}]}, {"name": "t:names.product_cards", "settings": [{"type": "checkbox", "id": "quick_add", "label": "t:settings.quick_add", "default": true}, {"type": "checkbox", "id": "mobile_quick_add", "label": "t:settings.mobile_quick_add", "default": false, "visible_if": "{{ settings.quick_add == true }}"}, {"type": "color_scheme", "id": "quick_add_color_scheme", "label": "t:settings.quick_add_colors", "default": "scheme-1", "visible_if": "{{ settings.quick_add == true }}"}, {"type": "header", "content": "t:settings.media"}, {"type": "checkbox", "id": "show_second_image_on_hover", "label": "t:settings.show_second_image_on_hover", "default": true}, {"type": "checkbox", "id": "product_card_carousel", "label": "t:settings.product_card_carousel", "default": true}]}, {"name": "t:names.search", "settings": [{"type": "collection", "id": "empty_state_collection", "label": "t:settings.empty_state_collection", "info": "t:settings.empty_state_collection_info"}, {"type": "header", "content": "t:names.predictive_search"}, {"type": "range", "id": "product_corner_radius", "min": 0, "max": 32, "step": 1, "unit": "px", "label": "t:settings.product_corner_radius", "default": 0}, {"type": "range", "id": "card_corner_radius", "min": 0, "max": 16, "step": 1, "unit": "px", "label": "t:settings.card_corner_radius", "default": 0}, {"type": "select", "id": "card_title_case", "label": "t:settings.product_and_card_title_case", "options": [{"value": "default", "label": "t:options.default"}, {"value": "uppercase", "label": "t:options.uppercase"}], "default": "default"}]}, {"name": "t:names.swatches", "settings": [{"type": "checkbox", "id": "show_variant_image", "label": "t:settings.variant_images", "default": false}, {"type": "range", "id": "variant_swatch_width", "min": 16, "max": 100, "step": 1, "unit": "px", "label": "t:settings.width", "default": 30}, {"type": "range", "id": "variant_swatch_height", "min": 16, "max": 100, "step": 1, "unit": "px", "label": "t:settings.height", "default": 30}, {"type": "range", "id": "variant_swatch_radius", "min": 0, "max": 100, "step": 1, "unit": "px", "label": "t:settings.border_radius", "default": 100}, {"type": "select", "id": "variant_swatch_border_style", "label": "t:settings.borders", "options": [{"value": "none", "label": "t:options.none"}, {"value": "solid", "label": "t:options.solid"}], "default": "solid"}, {"type": "range", "id": "variant_swatch_border_width", "min": 0, "max": 10, "step": 0.5, "unit": "px", "label": "t:settings.border_width", "default": 1, "visible_if": "{{ settings.variant_swatch_border_style != 'none' }}"}, {"type": "range", "id": "variant_swatch_border_opacity", "min": 0, "max": 100, "step": 1, "unit": "%", "label": "t:settings.border_opacity", "default": 10, "visible_if": "{{ settings.variant_swatch_border_style != 'none' }}"}]}, {"name": "t:names.variant_pickers", "settings": [{"type": "header", "content": "t:content.variant_settings"}, {"type": "header", "content": "t:content.buttons"}, {"type": "range", "id": "variant_button_border_width", "min": 0, "max": 4, "step": 1, "unit": "px", "label": "t:settings.border_width", "default": 1}, {"type": "range", "id": "variant_button_radius", "min": 0, "max": 100, "step": 1, "unit": "px", "label": "t:settings.border_radius", "default": 8}, {"type": "select", "id": "variant_button_width", "label": "t:settings.width", "options": [{"value": "default-width-buttons", "label": "t:options.fit"}, {"value": "equal-width-buttons", "label": "t:options.fill"}], "default": "equal-width-buttons"}]}]