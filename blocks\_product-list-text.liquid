{% # import schema from '../schemas/blocks/_product-list-text' %}

{% assign placeholder = 'content.featured_products' | t %}
{% assign placeholder = '<h3>' | append: placeholder | append: '</h3>' %}

{% render 'text', fallback_text: placeholder, block: block %}

{% schema %}
{
  "name": "t:names.collection_title",
  "tag": null,
  "settings": [
    {
      "type": "richtext",
      "id": "text",
      "label": "t:settings.text",
      "default": "t:html_defaults.share_information_about_your"
    },
    {
      "type": "header",
      "content": "t:content.layout"
    },
    {
      "type": "select",
      "id": "width",
      "label": "t:settings.width",
      "options": [
        {
          "value": "fit-content",
          "label": "t:options.fit"
        },
        {
          "value": "100%",
          "label": "t:options.fill"
        }
      ],
      "default": "fit-content"
    },
    {
      "type": "select",
      "id": "max_width",
      "label": "t:settings.max_width",
      "options": [
        {
          "value": "narrow",
          "label": "t:options.narrow"
        },
        {
          "value": "normal",
          "label": "t:options.normal"
        },
        {
          "value": "none",
          "label": "t:options.none"
        }
      ],
      "default": "normal"
    },
    {
      "type": "text_alignment",
      "id": "alignment",
      "label": "t:settings.alignment",
      "default": "left",
      "visible_if": "{{ block.settings.width == '100%' }}"
    },
    {
      "type": "header",
      "content": "t:content.typography"
    },
    {
      "type": "select",
      "id": "type_preset",
      "label": "t:settings.preset",
      "options": [
        {
          "value": "rte",
          "label": "t:options.default"
        },
        {
          "value": "paragraph",
          "label": "t:options.paragraph"
        },
        {
          "value": "h1",
          "label": "t:options.h1"
        },
        {
          "value": "h2",
          "label": "t:options.h2"
        },
        {
          "value": "h3",
          "label": "t:options.h3"
        },
        {
          "value": "h4",
          "label": "t:options.h4"
        },
        {
          "value": "h5",
          "label": "t:options.h5"
        },
        {
          "value": "h6",
          "label": "t:options.h6"
        },
        {
          "value": "custom",
          "label": "t:options.custom"
        }
      ],
      "default": "rte",
      "info": "t:info.edit_presets_in_theme_settings"
    },
    {
      "type": "select",
      "id": "font",
      "label": "t:settings.font",
      "options": [
        {
          "value": "var(--font-body--family)",
          "label": "t:options.body"
        },
        {
          "value": "var(--font-subheading--family)",
          "label": "t:options.subheading"
        },
        {
          "value": "var(--font-heading--family)",
          "label": "t:options.heading"
        },
        {
          "value": "var(--font-accent--family)",
          "label": "t:options.accent"
        }
      ],
      "default": "var(--font-body--family)",
      "visible_if": "{{ block.settings.type_preset == 'custom' }}"
    },
    {
      "type": "select",
      "id": "font_size",
      "label": "t:settings.size",
      "options": [
        {
          "value": "",
          "label": "t:options.default"
        },
        {
          "value": "0.625rem",
          "label": "10px"
        },
        {
          "value": "0.75rem",
          "label": "12px"
        },
        {
          "value": "0.875rem",
          "label": "14px"
        },
        {
          "value": "1rem",
          "label": "16px"
        },
        {
          "value": "1.125rem",
          "label": "18px"
        },
        {
          "value": "1.25rem",
          "label": "20px"
        },
        {
          "value": "1.5rem",
          "label": "24px"
        },
        {
          "value": "2rem",
          "label": "32px"
        },
        {
          "value": "2.5rem",
          "label": "40px"
        },
        {
          "value": "3rem",
          "label": "48px"
        },
        {
          "value": "3.5rem",
          "label": "56px"
        },
        {
          "value": "4.5rem",
          "label": "72px"
        },
        {
          "value": "5.5rem",
          "label": "88px"
        },
        {
          "value": "7.5rem",
          "label": "120px"
        },
        {
          "value": "9.5rem",
          "label": "152px"
        },
        {
          "value": "11.5rem",
          "label": "184px"
        }
      ],
      "default": "1rem",
      "visible_if": "{{ block.settings.type_preset == 'custom' }}"
    },
    {
      "type": "select",
      "id": "line_height",
      "label": "t:settings.line_height",
      "options": [
        {
          "value": "tight",
          "label": "t:options.tight"
        },
        {
          "value": "normal",
          "label": "t:options.normal"
        },
        {
          "value": "loose",
          "label": "t:options.loose"
        }
      ],
      "default": "normal",
      "visible_if": "{{ block.settings.type_preset == 'custom' }}"
    },
    {
      "type": "select",
      "id": "letter_spacing",
      "label": "t:settings.letter_spacing",
      "options": [
        {
          "value": "tight",
          "label": "t:options.tight"
        },
        {
          "value": "normal",
          "label": "t:options.normal"
        },
        {
          "value": "loose",
          "label": "t:options.loose"
        }
      ],
      "default": "normal",
      "visible_if": "{{ block.settings.type_preset == 'custom' }}"
    },
    {
      "type": "select",
      "id": "case",
      "label": "t:settings.case",
      "options": [
        {
          "value": "none",
          "label": "t:options.default"
        },
        {
          "value": "uppercase",
          "label": "t:options.uppercase"
        }
      ],
      "default": "none",
      "visible_if": "{{ block.settings.type_preset == 'custom' }}"
    },
    {
      "type": "select",
      "id": "wrap",
      "label": "t:settings.wrap",
      "options": [
        {
          "value": "pretty",
          "label": "t:options.pretty"
        },
        {
          "value": "balance",
          "label": "t:options.balance"
        },
        {
          "value": "nowrap",
          "label": "t:options.none"
        }
      ],
      "visible_if": "{{ block.settings.type_preset == 'custom' }}"
    },
    {
      "type": "select",
      "id": "color",
      "label": "t:settings.color",
      "options": [
        {
          "value": "var(--color-foreground)",
          "label": "t:options.text"
        },
        {
          "value": "var(--color-foreground-heading)",
          "label": "t:options.heading"
        },
        {
          "value": "var(--color-primary)",
          "label": "t:options.link"
        }
      ],
      "default": "var(--color-foreground)",
      "visible_if": "{{ block.settings.type_preset != 'rte' }}"
    },
    {
      "type": "header",
      "content": "t:content.appearance"
    },
    {
      "type": "checkbox",
      "id": "background",
      "label": "t:settings.background",
      "default": false
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "t:settings.background_color",
      "alpha": true,
      "default": "#00000026",
      "visible_if": "{{ block.settings.background }}"
    },
    {
      "type": "range",
      "id": "corner_radius",
      "label": "t:settings.corner_radius",
      "default": 0,
      "min": 0,
      "max": 50,
      "step": 1,
      "visible_if": "{{ block.settings.background }}"
    },
    {
      "type": "header",
      "content": "t:content.padding"
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "t:settings.top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "t:settings.bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-inline-start",
      "label": "t:settings.left",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-inline-end",
      "label": "t:settings.right",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "t:names.collection_title",
      "category": "t:categories.collection",
      "settings": {
        "text": "<h3>{{ closest.collection.title }}</h3>"
      }
    }
  ]
}
{% endschema %}
