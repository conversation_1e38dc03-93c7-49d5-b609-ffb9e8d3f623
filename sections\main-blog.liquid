{% # import schema from '../schemas/sections/main-blog' %}

<script
  src="{{ 'blog-posts-list.js' | asset_url }}"
  type="module"
></script>

<blog-posts-list section-id="{{ section.id }}">
  <div class="section-background color-{{ section.settings.color_scheme }}"></div>
  <div
    class="
      section
      color-{{ section.settings.color_scheme }}
      blog-posts
      spacing-style
      size-style
    "
    style="{% render 'spacing-style', settings: section.settings %}"
  >
    {% content_for 'blocks' %}

    {%- paginate blog.articles by 12 -%}
      <span ref="viewMorePrevious"></span>
      <div
        ref="grid"
        last-page="{{ paginate.pages }}"
        class="blog-posts-container"
      >
        {% for article in blog.articles %}
          <div
            class="blog-post-item"
            ref="cards[]"
            data-page="{{ paginate.current_page }}"
          >
            {% content_for 'block', id: 'static-blog-post-card', type: '_blog-post-card', article: article %}
          </div>
        {% endfor %}
      </div>
      <span ref="viewMoreNext"></span>
    {%- endpaginate -%}
  </div>
</blog-posts-list>

{% stylesheet %}
  /**
   * Blog posts page layout
   */
  .blog-posts {
    --page-content-width: var(--narrow-page-width);
    --page-width: calc(var(--page-content-width) + (var(--page-margin) * 2));
    --columns-gap: 36px;
    --rows-gap: 36px;
  }
  .blog-posts-container {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 1rem;
    width: 100%;
    column-gap: var(--columns-gap);
    row-gap: var(--rows-gap);
  }

  /**
   * Apart from the first and second rows in the grid, all remaining blog posts
   * are arranged in a three-column layout:
   * +------------+------------+-------------+
   * |            |            |             |
   * |  (span 2)  |  (span 2)  |  (span 2)   |
   * |            |            |             |
   * +------------+------------+-------------+
   */
  .blog-post-item {
    grid-column: span 2;
    --blog-post-card-scale: 0.6;

    @media screen and (max-width: 749px) {
      grid-column: span 6;
      --blog-post-card-scale: 0.5;
    }
  }

  /**
   * The second row of blog posts has two columns:
   * +-------------------+-------------------+
   * |                   |                   |
   * |  (column span 3)  |  (column span 3)  |
   * |                   |                   |
   * +-------------------+-------------------+
   */
  .blog-post-item:nth-child(2),
  .blog-post-item:nth-child(3) {
    grid-column: span 3;
    --blog-post-card-scale: 0.8;

    @media screen and (max-width: 749px) {
      grid-column: span 6;
      --blog-post-card-scale: 0.5;
    }
  }

  /**
   * The first row of blog posts has only one column:
   * +---------------------------------------+
   * |                                       |
   * |           (1 column span 6)           |
   * |                                       |
   * +---------------------------------------+
   */
  .blog-post-item:first-child {
    grid-column: span 6;
    --blog-post-card-scale: 1;
  }

  /**
   * When there's no image, the blog post item has a border.
   */
  .blog-post-item {
    border: 1px solid rgba(from var(--color-foreground) r g b / 0.2);
    padding: 0 1rem 1rem;
  }
  .blog-post-item:has(.blog-post-card__image-container) {
    border: none;
    padding: 0;
  }
{% endstylesheet %}

{% schema %}
{
  "name": "t:names.blog_posts",
  "blocks": [
    {
      "type": "@theme"
    },
    {
      "type": "@app"
    },
    {
      "type": "text"
    },
    {
      "type": "icon"
    },
    {
      "type": "image"
    },
    {
      "type": "button"
    },
    {
      "type": "video"
    },
    {
      "type": "group"
    },
    {
      "type": "spacer"
    },
    {
      "type": "_divider"
    }
  ],
  "disabled_on": {
    "groups": ["header"]
  },
  "settings": [
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:settings.color_scheme",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:content.padding"
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "t:settings.top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "t:settings.bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    }
  ]
}
{% endschema %}
