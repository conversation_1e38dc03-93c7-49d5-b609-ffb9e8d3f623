{% # import schema from '../schemas/blocks/_marquee' %}

<script
  src="{{ 'marquee.js' | asset_url }}"
  type="module"
></script>

{% assign gap_between_elements = block.settings.gap_between_elements %}

<marquee-component
  class="
    spacing-style
    gap-style
    {% if block.settings.inherit_color_scheme == false %}color-{{ block.settings.color_scheme }}{% endif %}
  "
  style="
    {%- render 'spacing-style', settings: block.settings %};
    {% render 'gap-style', value: gap_between_elements, name: 'marquee-gap' %}
    --marquee-direction: {{ block.settings.movement_direction }};
    {%- if block.settings.transparent_background == true -%}background-color: transparent;{%- endif -%}
  "
  data-speed-factor="20"
  data-movement-direction="{{ block.settings.movement_direction }}"
>
  <div
    class="marquee__wrapper"
    ref="wrapper"
  >
    <div
      class="marquee__content"
      ref="content"
    >
      <div class="marquee__repeated-items">
        {% content_for 'blocks' %}
      </div>
    </div>
  </div>
</marquee-component>

{% stylesheet %}
  marquee-component {
    display: block;
    width: 100%;
    overflow: hidden;
    background-color: var(--color-background);
  }

  .marquee__wrapper {
    display: flex;
    gap: var(--marquee-gap);
    width: fit-content;
    white-space: nowrap;
  }

  .marquee__content {
    min-width: max-content;
    display: flex;
    gap: var(--marquee-gap);
  }

  .marquee__content :is(p, h1, h2, h3, h4, h5, h6) {
    white-space: nowrap;
  }

  .marquee__content .marquee__repeated-items * {
    max-width: none;
  }

  .marquee__repeated-items {
    min-width: max-content;
    display: flex;
    gap: var(--marquee-gap);
    align-items: center;
    justify-content: center;
  }

  .marquee__repeated-items > * {
    align-content: center;
  }

  .hero__content-wrapper.layout-panel-flex--column marquee-component {
    --margin-inline: var(--full-page-margin-inline-offset);
    width: -webkit-fill-available;
    min-height: max-content;
  }

  @media (prefers-reduced-motion: no-preference) {
    marquee-component:not([data-disabled]) .marquee__wrapper {
      animation: marquee-motion var(--marquee-speed) linear infinite var(--marquee-direction);
    }
  }

  @keyframes marquee-motion {
    to {
      transform: translate3d(calc(-50% - (var(--marquee-gap) / 2)), 0, 0);
    }
  }
{% endstylesheet %}

{% schema %}
{
  "name": "t:names.marquee",
  "tag": null,
  "blocks": [
    {
      "type": "text"
    },
    {
      "type": "icon"
    },
    {
      "type": "_divider"
    }
  ],
  "settings": [
    {
      "type": "select",
      "id": "movement_direction",
      "label": "t:settings.motion_direction",
      "options": [
        {
          "value": "reverse",
          "label": "t:options.forward"
        },
        {
          "value": "normal",
          "label": "t:options.reverse"
        }
      ],
      "default": "normal"
    },
    {
      "type": "checkbox",
      "id": "inherit_color_scheme",
      "label": "t:settings.inherit_color_scheme",
      "default": true
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:settings.color_scheme",
      "default": "scheme-1",
      "visible_if": "{{ block.settings.inherit_color_scheme == false }}"
    },
    {
      "type": "checkbox",
      "id": "transparent_background",
      "label": "t:settings.transparent_background",
      "default": true
    },
    {
      "type": "header",
      "content": "t:content.padding"
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "t:settings.top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 24
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "t:settings.bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 24
    },
    {
      "type": "range",
      "id": "gap_between_elements",
      "label": "t:settings.gap",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 24
    }
  ],
  "presets": [
    {
      "name": "t:names.marquee",
      "category": "t:categories.decorative",
      "blocks": {
        "text": {
          "type": "text",
          "settings": {
            "text": "<p>We make things that work better and last longer.</p>",
            "type_preset": "custom",
            "font": "var(--font-body--family)",
            "font_size": "var(--font-size--h2)",
            "line_height": "tight",
            "letter_spacing": "tight",
            "case": "none",
            "wrap": "nowrap",
            "width": "fit-content"
          }
        }
      },
      "block_order": ["text"]
    }
  ]
}
{% endschema %}
