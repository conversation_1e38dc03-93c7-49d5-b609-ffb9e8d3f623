/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "main": {
      "type": "main-blog",
      "blocks": {
        "title": {
          "type": "text",
          "name": "Title",
          "settings": {
            "text": "<p>{{ closest.blog.title }}</p>",
            "width": "fit-content",
            "max_width": "narrow",
            "alignment": "left",
            "type_preset": "h2",
            "font": "var(--font-primary--family)",
            "font_size": "",
            "line_height": "normal",
            "letter_spacing": "normal",
            "case": "none",
            "wrap": "pretty",
            "color": "",
            "background": false,
            "background_color": "#00000026",
            "corner_radius": 0,
            "padding-block-start": 48,
            "padding-block-end": 48,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {}
        },
        "static-blog-post-card": {
          "type": "_blog-post-card",
          "static": true,
          "settings": {
            "alignment": "left"
          },
          "blocks": {
            "heading": {
              "type": "_heading",
              "name": "t:names.title",
              "static": true,
              "settings": {
                "type_preset": "h4",
                "font": "var(--font-primary--family)",
                "font_size": "",
                "line_height": "normal",
                "letter_spacing": "normal",
                "case": "none",
                "wrap": "pretty",
                "color": "",
                "text": "",
                "read_only": true,
                "alignment": "left",
                "show_alignment": false,
                "padding-block-start": 0,
                "padding-block-end": 10,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              },
              "blocks": {}
            },
            "blog-post-details": {
              "type": "_blog-post-info-text",
              "name": "t:names.details",
              "static": true,
              "settings": {
                "show_date": true,
                "show_author": false,
                "type_preset": "",
                "alignment": "left",
                "show_alignment": false,
                "padding-block-start": 0,
                "padding-block-end": 0
              },
              "blocks": {}
            },
            "image": {
              "type": "_blog-post-image",
              "static": true,
              "settings": {
                "height": "large",
                "border": "none",
                "border_width": 1,
                "border_opacity": 100,
                "border_radius": 4
              },
              "blocks": {}
            }
          },
          "block_order": []
        }
      },
      "block_order": [
        "title"
      ],
      "settings": {
        "color_scheme": "",
        "padding-block-start": 0,
        "padding-block-end": 48
      }
    }
  },
  "order": [
    "main"
  ]
}
