{% # import schema from '../schemas/sections/slideshow.js' %}

{% if section.blocks.size > 1 %}
  {% capture controls %}
    {%- render 'slideshow-controls',
      style: section.settings.slideshow_controls_style,
      autoplay: section.settings.autoplay,
      item_count: section.blocks.size,
      show_arrows: false,
      arrows_on_media: true,
      controls_on_media: true,
      pagination_position: 'center',
      icon_style: section.settings.icons_style
    -%}
  {% endcapture %}
{% endif %}

{% capture slides %}
  {% content_for 'blocks' %}
{% endcapture %}

{% liquid
  assign slideshow_class = 'slideshow--single-media'
  if section.blocks.size > 1
    assign slideshow_class = ''
  endif

  assign show_arrows = true
  if section.settings.icons_style == 'none' or section.blocks.size <= 1
    assign show_arrows = false
  endif
%}

<div
  class="spacing-style section section--{{ section.settings.section_width }} color-{{ section.settings.color_scheme }} disable-section-top-offset"
  style="{% render 'spacing-padding', settings: section.settings %}"
>
  {% render 'slideshow',
    class: slideshow_class,
    controls: controls,
    show_arrows: show_arrows,
    icon_style: section.settings.icons_style,
    autoplay: section.settings.autoplay,
    autoplay_speed: section.settings.autoplay_speed,
    slides: slides,
    slide_count: section.blocks.size,
    slide_size: section.settings.slide_height
  %}
</div>

{% schema %}
{
  "name": "t:names.slideshow",
  "class": "container-background-image",
  "blocks": [
    {
      "type": "_slide"
    }
  ],
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "select",
      "id": "icons_style",
      "label": "t:settings.navigation",
      "options": [
        {
          "value": "arrow",
          "label": "t:options.arrows"
        },
        {
          "value": "chevron",
          "label": "t:options.chevrons"
        },
        {
          "value": "arrows_large",
          "label": "t:options.large_arrows"
        },
        {
          "value": "chevron_large",
          "label": "t:options.large_chevrons"
        },
        {
          "value": "none",
          "label": "t:options.none"
        }
      ],
      "default": "arrows_large"
    },
    {
      "type": "select",
      "id": "slideshow_controls_style",
      "label": "t:settings.pagination",
      "options": [
        {
          "value": "dots",
          "label": "t:options.dots"
        },
        {
          "value": "counter",
          "label": "t:options.counter"
        }
      ],
      "default": "dots"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:settings.color_scheme",
      "default": "primary"
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "t:settings.auto_rotate_slides",
      "default": false
    },
    {
      "type": "range",
      "id": "autoplay_speed",
      "label": "t:settings.speed",
      "min": 3,
      "max": 7,
      "step": 1,
      "unit": "s",
      "default": 4,
      "visible_if": "{{ section.settings.autoplay }}"
    },
    {
      "type": "header",
      "content": "t:content.size"
    },
    {
      "type": "select",
      "id": "section_width",
      "label": "t:settings.width",
      "options": [
        {
          "value": "page-width",
          "label": "t:options.page"
        },
        {
          "value": "full-width",
          "label": "t:options.full"
        }
      ],
      "default": "full-width"
    },
    {
      "type": "select",
      "id": "slide_height",
      "options": [
        {
          "value": "auto",
          "label": "t:options.auto"
        },
        {
          "value": "small",
          "label": "t:options.small"
        },
        {
          "value": "medium",
          "label": "t:options.medium"
        },
        {
          "value": "large",
          "label": "t:options.large"
        }
      ],
      "default": "medium",
      "label": "t:settings.height"
    },
    {
      "type": "header",
      "content": "t:content.padding"
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "t:settings.top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "t:settings.bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "t:names.slideshow",
      "category": "t:categories.storytelling",
      "blocks": {
        "slide_1": {
          "type": "_slide",
          "settings": {
            "horizontal_alignment_flex_direction_column": "center"
          },
          "blocks": {
            "group": {
              "type": "group",
              "settings": {
                "horizontal_alignment_flex_direction_column": "center",
                "width": "custom",
                "custom_width": 50,
                "width_mobile": "custom",
                "inherit_color_scheme": false,
                "color_scheme": "scheme-6",
                "custom_width_mobile": 85,
                "padding-inline-start": 48,
                "padding-inline-end": 48,
                "padding-block-start": 48,
                "padding-block-end": 48
              },
              "blocks": {
                "heading": {
                  "type": "text",
                  "settings": {
                    "text": "<h2>New arrivals</h2>"
                  }
                },
                "text": {
                  "type": "text",
                  "settings": {
                    "text": "<p>Introducing our latest products, made especially for the season. Shop your favorites before they're gone!</p>",
                    "padding-block-end": 20
                  }
                },
                "button": {
                  "type": "button",
                  "settings": {
                    "label": "Shop now",
                    "link": "shopify://collections/all"
                  }
                }
              },
              "block_order": ["heading", "text", "button"]
            }
          },
          "block_order": ["group"]
        },
        "slide_2": {
          "type": "_slide",
          "settings": {
            "horizontal_alignment_flex_direction_column": "center"
          },
          "blocks": {
            "group": {
              "type": "group",
              "settings": {
                "horizontal_alignment_flex_direction_column": "center",
                "width": "custom",
                "custom_width": 50,
                "width_mobile": "custom",
                "inherit_color_scheme": false,
                "color_scheme": "scheme-6",
                "custom_width_mobile": 85,
                "padding-inline-start": 48,
                "padding-inline-end": 48,
                "padding-block-start": 48,
                "padding-block-end": 48
              },
              "blocks": {
                "heading": {
                  "type": "text",
                  "settings": {
                    "text": "<h2>Bestsellers</h2>"
                  }
                },
                "text": {
                  "type": "text",
                  "settings": {
                    "text": "<p>Discover the bestsellers that have captured the hearts of our customers with their perfect blend of functionality and style.</p>",
                    "padding-block-end": 20
                  }
                },
                "button": {
                  "type": "button",
                  "settings": {
                    "label": "Shop now",
                    "link": "shopify://collections/all"
                  }
                }
              },
              "block_order": ["heading", "text", "button"]
            }
          },
          "block_order": ["group"]
        }
      },
      "block_order": ["slide_1", "slide_2"]
    }
  ]
}
{% endschema %}
