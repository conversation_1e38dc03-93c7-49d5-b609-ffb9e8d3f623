/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin language editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "blocks": {
    "load_video": "Video laden: {{ description }}",
    "sold_out": "Ausverkauft",
    "email_signup": {
      "label": "E-Mail",
      "placeholder": "E-Mail-Adresse",
      "success": "Danke für deine Anmeldung!"
    },
    "filter": "Filtern",
    "payment_methods": "Zahlungsmethoden",
    "contact_form": {
      "name": "Name",
      "email": "E-Mail-Adresse",
      "phone": "Telefonnummer",
      "comment": "Kommentar",
      "post_success": "Dan<PERSON>, dass du uns kontaktiert hast. Wir werden uns so schnell wie möglich bei dir melden.",
      "error_heading": "Bitte passe Folgendes an:"
    }
  },
  "accessibility": {
    "play_model": "3D-Modell abspielen",
    "play_video": "Video abspielen",
    "unit_price": "Grundpreis",
    "country_results_count": "{{ count }} Ergebnisse",
    "slideshow_pause": "Slideshow pausieren",
    "slideshow_play": "Slideshow abspielen",
    "remove_item": "Entferne {{ title}}",
    "skip_to_text": "Direkt zum Inhalt",
    "skip_to_product_info": "Zu Produktinformationen springen",
    "skip_to_results_list": "Zur Ergebnisliste springen",
    "new_window": "Wird in einem neuen Fenster geöffnet.",
    "slideshow_next": "Nächste Folie",
    "slideshow_previous": "Vorherige Folie",
    "close_dialog": "Dialogfeld schließen",
    "reset_search": "Suche zurücksetzen",
    "search_results_count": "{{ count }} Suchergebnisse für „{{ query }}“ gefunden",
    "search_results_no_results": "Keine Ergebnisse für „{{ query }}“ gefunden",
    "filters": "Filter",
    "account": "Kontomenü öffnen",
    "cart": "Warenkorb",
    "cart_count": "Artikel im Warenkorb insgesamt",
    "filter_count": {
      "one": "{{ count }} Filter angewandt",
      "other": "{{ count }} Filter angewandt"
    },
    "menu": "Menü",
    "country_region": "Land/Region",
    "slide_status": "Folie {{ index }} von {{ length }}",
    "scroll_to": "Zu {{ title }} scrollen",
    "discount": "Rabattcode anwenden",
    "loading_product_recommendations": "Produktempfehlungen werden geladen",
    "discount_applied": "Angewendeter Rabattcode: {{ code }}",
    "open_cart_drawer": "Warenkorb öffnen",
    "inventory_status": "Inventarstatus",
    "pause_video": "Video pausieren",
    "find_country": "Land finden",
    "localization_region_and_language": "Region- und Sprachauswahl öffnen",
    "open_search_modal": "Suche öffnen",
    "decrease_quantity": "Menge verringern",
    "increase_quantity": "Menge erhöhen",
    "quantity": "Anzahl",
    "rating": "Die Bewertung für dieses Produkts lautet {{ rating }} von 5 Sternen"
  },
  "actions": {
    "add_to_cart": "In den Warenkorb legen",
    "clear_all": "Alles löschen",
    "remove": "Entfernen",
    "view_in_your_space": "In deinem Bereich anzeigen",
    "show_filters": "Filtern",
    "clear": "Löschen",
    "continue_shopping": "Weiter einkaufen",
    "log_in_html": "Hast du ein Konto? <a href=\"{{ link }}\">Logge dich ein</a>, damit es beim Checkout schneller geht.",
    "see_items": {
      "one": "{{ count }} Artikel anzeigen",
      "other": "{{ count }} Artikel anzeigen"
    },
    "view_all": "Alle anzeigen",
    "add": "Hinzufügen",
    "choose": "Auswählen",
    "added": "Hinzugefügt",
    "show_less": "Weniger anzeigen",
    "show_more": "Mehr anzeigen",
    "close": "Schließen",
    "more": "Mehr",
    "reset": "Zurücksetzen",
    "zoom": "Zoomen",
    "close_dialog": "Dialogfeld schließen",
    "back": "Zurück",
    "log_in": "Anmelden",
    "log_out": "Abmelden",
    "remove_discount": "Rabatt {{ code }} entfernen",
    "enter_using_password": "Mit Passwort anmelden",
    "submit": "Senden",
    "enter_password": "Passwort eingeben",
    "view_store_information": "Shop-Informationen anzeigen",
    "apply": "Anwenden",
    "sign_in_options": "Andere Anmeldeoptionen",
    "sign_up": "Registrieren",
    "open_image_in_full_screen": "Bild im Vollbildmodus öffnen",
    "sort": "Sortieren",
    "show_all_options": "Alle Optionen anzeigen"
  },
  "content": {
    "reviews": "Reviews",
    "language": "Sprache",
    "localization_region_and_language": "Region und Sprache",
    "no_results_found": "Keine Ergebnisse gefunden",
    "cart_total": "Gesamtbetrag im Warenkorb",
    "your_cart_is_empty": "Dein Warenkorb ist leer",
    "product_image": "Produktbild",
    "product_information": "Produktinformationen",
    "quantity": "Anzahl",
    "product_total": "Produkt insgesamt",
    "cart_estimated_total": "Geschätzter Gesamtbetrag",
    "seller_note": "Besondere Anweisungen",
    "cart_subtotal": "Zwischensumme",
    "discounts": "Rabatte",
    "discount": "Rabatt",
    "duties_and_taxes_included_shipping_at_checkout_with_policy_html": "Inkl. Zollgebühren und Steuern. Rabatte und <a href=\"{{ link }}\">Versand</a> werden beim Checkout berechnet.",
    "duties_and_taxes_included_shipping_at_checkout_without_policy": "Inkl. Zollgebühren und Steuern. Rabatte und Versand werden beim Checkout berechnet.",
    "taxes_included_shipping_at_checkout_with_policy_html": "Inkl. Steuern. Rabatte und <a href=\"{{ link }}\">Versand</a> werden beim Checkout berechnet.",
    "taxes_included_shipping_at_checkout_without_policy": "Inkl. Steuern. Rabatte und Versand werden beim Checkout berechnet.",
    "duties_included_taxes_at_checkout_shipping_at_checkout_with_policy_html": "Inkl. Zollgebühren. Steuern, Rabatte und <a href=\"{{ link }}\">Versand</a> werden beim Checkout berechnet.",
    "duties_included_taxes_at_checkout_shipping_at_checkout_without_policy": "Inkl. Zollgebühren. Steuern, Rabatte und Versand werden beim Checkout berechnet.",
    "taxes_at_checkout_shipping_at_checkout_with_policy_html": "Steuern, Rabatte und <a href=\"{{ link }}\">Versand</a> werden beim Checkout berechnet.",
    "taxes_at_checkout_shipping_at_checkout_without_policy": "Steuern, Rabatte und Versand werden beim Checkout berechnet.",
    "checkout": "Auschecken",
    "cart_title": "Warenkorb",
    "price": "Preis",
    "price_regular": "Normaler Preis",
    "price_compare_at": "Vergleichspreis",
    "price_sale": "Angebotspreis",
    "duties_and_taxes_included": "Inkl. Zollgebühren und Steuern.",
    "duties_included": "Inkl. Zollgebühren.",
    "shipping_policy_html": "<a href=\"{{ link }}\">Versand</a> wird beim Checkout berechnet.",
    "taxes_included": "Inkl. Steuern.",
    "product_badge_sold_out": "Ausverkauft",
    "product_badge_sale": "Sale",
    "search_input_label": "Suchen",
    "search_input_placeholder": "Suchen",
    "search_results": "Suchergebnisse",
    "search_results_label": "Suchergebnisse",
    "search_results_no_results": "Keine Ergebnisse für „{{ terms }}“ gefunden. Versuche es mit einem anderen Suchbegriff.",
    "search_results_resource_articles": "Blog-Beiträge",
    "search_results_resource_collections": "Kollektionen",
    "search_results_resource_pages": "Seiten",
    "search_results_resource_products": "Produkte",
    "search_results_resource_queries": "Suchvorschläge",
    "search_results_view_all": "Alle anzeigen",
    "search_results_view_all_button": "Alle anzeigen",
    "search_results_resource_products_count": {
      "one": "{{ count }} Produkt",
      "other": "{{ count }} Produkte"
    },
    "grid_view": {
      "default_view": "Standard",
      "grid_fieldset": "Spaltenraster",
      "single_item": "Einzeln",
      "zoom_out": "Herauszoomen"
    },
    "recently_viewed_products": "Zuletzt angesehen",
    "unavailable": "Nicht verfügbar",
    "collection_placeholder": "Kollektionstitel",
    "product_card_placeholder": "Produkttitel",
    "product_count": "Produktanzahl",
    "item_count": {
      "one": "{{ count }} Artikel",
      "other": "{{ count }} Artikel"
    },
    "errors": "Fehler",
    "price_from": "Ab {{ price }}",
    "search": "Suche",
    "search_results_no_results_check_spelling": "Keine Ergebnisse für „{{ terms }}“ gefunden. Überprüfe die Schreibweise oder versuche es mit einer anderen Suchanfrage.",
    "featured_products": "Vorgestellte Produkte",
    "filters": "Filter",
    "no_products_found": "Keine Produkte gefunden.",
    "price_filter_html": "Der höchste Preis ist {{ price }}",
    "use_fewer_filters_html": "Versuche, weniger Filter zu verwenden oder <a class=\"{{ class }}\" href=\"{{ link }}\">alle Filter zu löschen</a>.",
    "account_title": "Konto",
    "account_title_personalized": "Hallo {{ first_name }}",
    "account_orders": "Bestellungen",
    "account_profile": "Profil",
    "blog_details_separator": "|",
    "discount_code": "Rabattcode",
    "duties_and_taxes_included_shipping_at_checkout_with_policy_without_discounts_html": "Inkl. Zollgebühren und Steuern. Versand wird beim Checkout berechnet.",
    "duties_and_taxes_included_shipping_at_checkout_without_policy_without_discounts": "Inkl. Zollgebühren und Steuern. Versand wird beim Checkout berechnet.",
    "duties_included_taxes_at_checkout_shipping_at_checkout_with_policy_without_discounts_html": "Inkl. Zollgebühren. Versand wird beim Checkout berechnet.",
    "duties_included_taxes_at_checkout_shipping_at_checkout_without_policy_without_discounts": "Inkl. Zollgebühren. Versand wird beim Checkout berechnet.",
    "pickup_available_at_html": "Abholung bei <b>{{ location }}</b> verfügbar",
    "pickup_available_in": "Abholung verfügbar, {{ pickup_time }}",
    "pickup_not_available": "Abholung derzeit nicht verfügbar",
    "pickup_ready_in": "{{ pickup_time }}",
    "read_more": "Mehr lesen …",
    "taxes_at_checkout_shipping_at_checkout_with_policy_without_discounts_html": "Steuern und <a href=\"{{ link }}\">Versand</a> werden beim Checkout berechnet.",
    "taxes_at_checkout_shipping_at_checkout_without_policy_without_discounts": "Steuern und Versand werden beim Checkout berechnet.",
    "taxes_included_shipping_at_checkout_with_policy_without_discounts_html": "Inkl. Steuern. Versand wird beim Checkout berechnet.",
    "taxes_included_shipping_at_checkout_without_policy_without_discounts": "Inkl. Steuern. Versand wird beim Checkout berechnet.",
    "wrong_password": "Falsches Passwort",
    "view_more_details": "Weitere Details anzeigen",
    "inventory_low_stock": "Niedriger Lagerbestand",
    "inventory_in_stock": "Auf Lager",
    "inventory_out_of_stock": "Nicht vorrätig",
    "page_placeholder_title": "Seitentitel",
    "page_placeholder_content": "Wähle eine Seite aus, die angezeigt werden soll.",
    "placeholder_image": "Platzhalterbild",
    "shipping_policy": "Versandkosten werden beim Checkout berechnet.",
    "inventory_low_stock_show_count": {
      "one": "{{ count }} übrig",
      "other": "{{ count }} übrig"
    },
    "powered_by": "Dieser Shop wird unterstützt werden von",
    "store_owner_link_html": "Bist du der Shop-Inhaber? <a href=\"{{ link }}\">Hier einloggen</a>",
    "shipping_discount_error": "Versandrabatte werden beim Checkout angezeigt, nachdem die Adresse hinzugefügt wurde",
    "discount_code_error": "Rabattcode kann nicht auf den Warenkorb angewendet werden"
  },
  "gift_cards": {
    "issued": {
      "how_to_use_gift_card": "Verwende diesen Geschenkgutscheincode online oder verwende den QR-Code im Shop",
      "title": "Hier ist dein {{ value }}-Gutschein für {{ shop }}!",
      "subtext": "Dein Gutschein",
      "shop_link": "Onlineshop besuchen",
      "add_to_apple_wallet": "Zu Apple Wallet hinzufügen",
      "qr_image_alt": "QR-Code – Scannen, um Gutschein einzulösen",
      "copy_code": "Geschenkgutscheincode kopieren",
      "expiration_date": "Gültig bis {{ expires_on }}",
      "copy_code_success": "Code erfolgreich kopiert",
      "expired": "Abgelaufen"
    }
  },
  "placeholders": {
    "password": "Passwort",
    "search": "Suchen",
    "product_title": "Produkttitel",
    "collection_title": "Kollektionstitel"
  },
  "products": {
    "product": {
      "add_to_cart": "In den Warenkorb legen",
      "added_to_cart": "Zum Warenkorb hinzugefügt",
      "adding_to_cart": "Wird hinzugefügt...",
      "add_to_cart_error": "Fehler beim Hinzufügen zum Warenkorb",
      "sold_out": "Ausverkauft",
      "unavailable": "Nicht verfügbar"
    }
  },
  "fields": {
    "separator": "bis"
  },
  "blogs": {
    "article": {
      "comment_author_separator": "•",
      "comments_heading": {
        "one": "{{ count }} Kommentar",
        "other": "{{ count }} Kommentare"
      }
    },
    "comment_form": {
      "email": "E-Mail",
      "error": "Kommentar konnte nicht gepostet werden. Bitte beachte Folgendes:",
      "heading": "Hinterlasse einen Kommentar",
      "message": "Nachricht",
      "moderated": "Bitte beachte, dass Kommentare vor der Veröffentlichung freigegeben werden müssen.",
      "name": "Name",
      "post": "Kommentar posten",
      "success_moderated": "Kommentar gepostet, Moderation ausstehend",
      "success": "Kommentar gepostet"
    }
  }
}
