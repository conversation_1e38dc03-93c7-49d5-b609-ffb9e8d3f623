{% # import schema from '../schemas/blocks/footer-utilities.js' %}

<div
  class="footer-utilities spacing-style"
  style="{% render 'spacing-style', settings: block.settings %} --border-width: {{ block.settings.divider_thickness }}px;"
  {{ block.shopify_attributes }}
>
  <div class="footer-utilities__group footer-utilities__group--left">
    {% content_for 'block', type: '_footer-copyright', id: 'copyright' %}
    {% content_for 'block', type: '_footer-policy-list', id: 'policy_list' %}
  </div>

  <div class="footer-utilities__group footer-utilities__group--right">
    {% content_for 'block', type: '_footer-social-icons', id: 'social_icons' %}
  </div>
</div>

{% stylesheet %}
  .footer-utilities {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--gap-lg);
    text-wrap: nowrap;
    border-top: var(--border-width) solid var(--color-border);
    color: var(--color-foreground-muted);

    @media screen and (min-width: 750px) {
      flex-direction: row;
      justify-content: space-between;
      gap: var(--gap-md);
      align-items: center;
      text-align: left;
    }
  }

  .footer-utilities a {
    color: var(--color-foreground-muted);
  }

  .footer-utilities__group {
    width: 100%;
    display: flex;
    flex: 1 1 max-content;
    text-align: center;
    flex-direction: column;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--gap-md);

    @media screen and (min-width: 750px) {
      flex-direction: row;
      align-items: baseline;
      gap: var(--gap-2xs) var(--gap-xl);
      text-align: left;
    }
  }

  .footer-utilities__group--right {
    @media screen and (min-width: 750px) {
      justify-content: flex-end;
    }
  }

  .footer-utilities__group:empty {
    @media screen and (max-width: 749px) {
      display: none;
    }
  }
{% endstylesheet %}

{% schema %}
{
  "name": "t:names.footer_utilities",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "t:content.appearance"
    },
    {
      "type": "range",
      "id": "divider_thickness",
      "label": "t:settings.divider_thickness",
      "min": 0,
      "max": 5,
      "step": 0.5,
      "unit": "px",
      "default": 1
    },
    {
      "type": "header",
      "content": "t:content.padding"
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "t:settings.top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 20
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "t:settings.bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 20
    }
  ],
  "presets": [
    {
      "name": "t:names.footer_utilities",
      "category": "t:categories.footer"
    }
  ]
}
{% endschema %}
