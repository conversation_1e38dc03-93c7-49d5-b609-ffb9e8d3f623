{% # import schema from '../schemas/blocks/_accordion-row.js' %}

<accordion-custom
  {% if block.settings.open_by_default %}
    open-by-default-on-desktop
    open-by-default-on-mobile
  {% endif %}
>
  <details
    class="details"
    {{ block.shopify_attributes }}
  >
    <summary class="details__header">
      {% render 'icon-or-image',
        icon: block.settings.icon,
        image_upload: block.settings.image_upload,
        width: block.settings.width,
        class_name: 'details__icon'
      %}
      {{ block.settings.heading }}
      <span class="svg-wrapper icon-caret icon-animated">
        {{- 'icon-caret.svg' | inline_asset_content -}}
      </span>
      <span class="svg-wrapper icon-plus">
        {{- 'icon-plus.svg' | inline_asset_content -}}
      </span>
    </summary>

    <div class="details-content">
      {% content_for 'blocks' %}
    </div>
  </details>
</accordion-custom>

{% stylesheet %}
  .details__icon {
    height: auto;
    margin-inline-end: var(--margin-xs);
  }
{% endstylesheet %}

{% schema %}
{
  "name": "t:names.accordion_row",
  "tag": null,
  "blocks": [
    {
      "type": "@theme"
    },
    {
      "type": "@app"
    }
  ],
  "settings": [
    {
      "type": "text",
      "id": "heading",
      "label": "t:settings.heading",
      "default": "t:text_defaults.accordion_heading"
    },
    {
      "type": "checkbox",
      "id": "open_by_default",
      "label": "t:settings.open_row_by_default",
      "default": false
    },
    {
      "type": "header",
      "content": "t:content.icon"
    },
    {
      "type": "select",
      "id": "icon",
      "label": "t:settings.icon",
      "options": [
        {
          "value": "none",
          "label": "t:options.none"
        },
        {
          "value": "apple",
          "label": "t:options.apple"
        },
        {
          "value": "arrow",
          "label": "t:options.arrow"
        },
        {
          "value": "banana",
          "label": "t:options.banana"
        },
        {
          "value": "bottle",
          "label": "t:options.bottle"
        },
        {
          "value": "box",
          "label": "t:options.box"
        },
        {
          "value": "carrot",
          "label": "t:options.carrot"
        },
        {
          "value": "chat_bubble",
          "label": "t:options.chat_bubble"
        },
        {
          "value": "check_box",
          "label": "t:options.check_box"
        },
        {
          "value": "clipboard",
          "label": "t:options.clipboard"
        },
        {
          "value": "dairy",
          "label": "t:options.dairy"
        },
        {
          "value": "dairy_free",
          "label": "t:options.dairy_free"
        },
        {
          "value": "dryer",
          "label": "t:options.dryer"
        },
        {
          "value": "eye",
          "label": "t:options.eye"
        },
        {
          "value": "fire",
          "label": "t:options.fire"
        },
        {
          "value": "gluten_free",
          "label": "t:options.gluten_free"
        },
        {
          "value": "heart",
          "label": "t:options.heart"
        },
        {
          "value": "iron",
          "label": "t:options.iron"
        },
        {
          "value": "leaf",
          "label": "t:options.leaf"
        },
        {
          "value": "leather",
          "label": "t:options.leather"
        },
        {
          "value": "lightning_bolt",
          "label": "t:options.lightning_bolt"
        },
        {
          "value": "lipstick",
          "label": "t:options.lipstick"
        },
        {
          "value": "lock",
          "label": "t:options.lock"
        },
        {
          "value": "map_pin",
          "label": "t:options.map_pin"
        },
        {
          "value": "nut_free",
          "label": "t:options.nut_free"
        },
        {
          "value": "pants",
          "label": "t:options.pants"
        },
        {
          "value": "paw_print",
          "label": "t:options.paw_print"
        },
        {
          "value": "pepper",
          "label": "t:options.pepper"
        },
        {
          "value": "perfume",
          "label": "t:options.perfume"
        },
        {
          "value": "plane",
          "label": "t:options.plane"
        },
        {
          "value": "plant",
          "label": "t:options.plant"
        },
        {
          "value": "price_tag",
          "label": "t:options.price_tag"
        },
        {
          "value": "question_mark",
          "label": "t:options.question_mark"
        },
        {
          "value": "recycle",
          "label": "t:options.recycle"
        },
        {
          "value": "return",
          "label": "t:options.return"
        },
        {
          "value": "ruler",
          "label": "t:options.ruler"
        },
        {
          "value": "serving_dish",
          "label": "t:options.serving_dish"
        },
        {
          "value": "shirt",
          "label": "t:options.shirt"
        },
        {
          "value": "shoe",
          "label": "t:options.shoe"
        },
        {
          "value": "silhouette",
          "label": "t:options.silhouette"
        },
        {
          "value": "bluesky",
          "label": "t:options.social_bluesky"
        },
        {
          "value": "facebook",
          "label": "t:options.social_facebook"
        },
        {
          "value": "instagram",
          "label": "t:options.social_instagram"
        },
        {
          "value": "linkedin",
          "label": "t:options.social_linkedin"
        },
        {
          "value": "pinterest",
          "label": "t:options.social_pinterest"
        },
        {
          "value": "snapchat",
          "label": "t:options.social_snapchat"
        },
        {
          "value": "spotify",
          "label": "t:options.social_spotify"
        },
        {
          "value": "threads",
          "label": "t:options.social_threads"
        },
        {
          "value": "tiktok",
          "label": "t:options.social_tiktok"
        },
        {
          "value": "tumblr",
          "label": "t:options.social_tumblr"
        },
        {
          "value": "twitter",
          "label": "t:options.social_twitter"
        },
        {
          "value": "vimeo",
          "label": "t:options.social_vimeo"
        },
        {
          "value": "youtube",
          "label": "t:options.social_youtube"
        },
        {
          "value": "whatsapp",
          "label": "t:options.social_whatsapp"
        },
        {
          "value": "snowflake",
          "label": "t:options.snowflake"
        },
        {
          "value": "star",
          "label": "t:options.star"
        },
        {
          "value": "stopwatch",
          "label": "t:options.stopwatch"
        },
        {
          "value": "truck",
          "label": "t:options.truck"
        },
        {
          "value": "washing",
          "label": "t:options.washing"
        }
      ],
      "default": "none"
    },
    {
      "type": "image_picker",
      "id": "image_upload",
      "label": "t:settings.image_icon"
    },
    {
      "type": "range",
      "id": "width",
      "label": "t:settings.width",
      "min": 12,
      "max": 200,
      "step": 2,
      "unit": "px",
      "default": 20
    }
  ],
  "presets": [
    {
      "name": "t:names.accordion_row",
      "blocks": {
        "text-1": {
          "type": "text",
          "settings": {
            "text": "<p>We will work quickly to ship your order as soon as possible. Once your order has shipped, you will receive an email with further information. Delivery times vary depending on your location.</p>",
            "width": "100%"
          }
        }
      },
      "block_order": ["text-1"],
      "settings": {
        "heading": "When will I get my order?"
      }
    }
  ]
}
{% endschema %}
