/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "current": {
    "type_body_font": "inter_n4",
    "type_subheading_font": "inter_n5",
    "type_heading_font": "inter_n7",
    "type_accent_font": "bricolage_grotesque_n7",
    "type_size_paragraph": "14",
    "type_line_height_paragraph": "body-loose",
    "type_font_h1": "accent",
    "type_size_h1": "88",
    "type_line_height_h1": "display-tight",
    "type_letter_spacing_h1": "heading-normal",
    "type_case_h1": "none",
    "type_font_h2": "heading",
    "type_size_h2": "56",
    "type_line_height_h2": "display-loose",
    "type_letter_spacing_h2": "heading-normal",
    "type_case_h2": "none",
    "type_font_h3": "heading",
    "type_size_h3": "32",
    "type_line_height_h3": "display-normal",
    "type_letter_spacing_h3": "heading-normal",
    "type_case_h3": "none",
    "type_font_h4": "heading",
    "type_size_h4": "24",
    "type_line_height_h4": "display-tight",
    "type_font_h5": "subheading",
    "type_size_h5": "14",
    "type_line_height_h5": "display-loose",
    "type_font_h6": "subheading",
    "type_size_h6": "12",
    "type_line_height_h6": "display-loose",
    "page_width": "narrow",
    "card_hover_effect": "none",
    "badge_position": "top-right",
    "badge_corner_radius": 100,
    "badge_sale_color_scheme": "scheme-1",
    "badge_sold_out_color_scheme": "scheme-3",
    "badge_text_transform": "none",
    "primary_button_border_width": 0,
    "button_border_radius_primary": 14,
    "secondary_button_border_width": 1,
    "button_border_radius_secondary": 14,
    "button_font_weight_secondary": "default",
    "cart_type": "drawer",
    "cart_price_font": "secondary",
    "show_cart_note": false,
    "show_add_discount_code": true,
    "drawer_drop_shadow": true,
    "icon_stroke": "default",
    "input_border_width": 1,
    "inputs_border_radius": 4,
    "popover_border_radius": 14,
    "popover_border": "none",
    "currency_code_enabled_product_pages": false,
    "currency_code_enabled_product_cards": false,
    "currency_code_enabled_cart_items": false,
    "product_corner_radius": 0,
    "card_corner_radius": 4,
    "show_variant_image": false,
    "variant_swatch_width": 34,
    "variant_swatch_height": 34,
    "variant_swatch_radius": 32,
    "variant_button_border_width": 1,
    "variant_button_radius": 14,
    "variant_button_width": "equal-width-buttons",
    "content_for_index": [],
    "color_schemes": {
      "scheme-1": {
        "settings": {
          "background": "#ffffff",
          "foreground_heading": "#000000",
          "foreground": "#000000cf",
          "primary": "#000000cf",
          "primary_hover": "#000000",
          "border": "#0000000f",
          "shadow": "#000000",
          "primary_button_background": "#000000",
          "primary_button_text": "#ffffff",
          "primary_button_border": "#000000",
          "primary_button_hover_background": "#333333",
          "primary_button_hover_text": "#ffffff",
          "primary_button_hover_border": "#000000",
          "secondary_button_background": "#0000000f",
          "secondary_button_text": "#000000",
          "secondary_button_border": "#0000000f",
          "secondary_button_hover_background": "#333333",
          "secondary_button_hover_text": "#ffffff",
          "secondary_button_hover_border": "#333333",
          "input_background": "#ffffffc7",
          "input_text_color": "#333333",
          "input_border_color": "#f5f5f5",
          "input_hover_background": "#00000003",
          "variant_background_color": "#ffffff",
          "variant_text_color": "#000000",
          "variant_border_color": "#00000021",
          "variant_hover_background_color": "#f5f5f5",
          "variant_hover_text_color": "#000000",
          "variant_hover_border_color": "#e6e6e6",
          "selected_variant_background_color": "#000000",
          "selected_variant_text_color": "#ffffff",
          "selected_variant_border_color": "#000000",
          "selected_variant_hover_background_color": "#1a1a1a",
          "selected_variant_hover_text_color": "#ffffff",
          "selected_variant_hover_border_color": "#1a1a1a"
        }
      },
      "scheme-2": {
        "settings": {
          "background": "#f5f5f5",
          "foreground_heading": "#000000",
          "foreground": "#000000cf",
          "primary": "#000000cf",
          "primary_hover": "#000000",
          "border": "#DFDFDF",
          "shadow": "#000000",
          "primary_button_background": "#000000",
          "primary_button_text": "#ffffff",
          "primary_button_border": "#000000",
          "primary_button_hover_background": "#333333",
          "primary_button_hover_text": "#ffffff",
          "primary_button_hover_border": "#333333",
          "secondary_button_background": "#ffffffb0",
          "secondary_button_text": "#000000",
          "secondary_button_border": "#ffffffb0",
          "secondary_button_hover_background": "#ffffff",
          "secondary_button_hover_text": "#000000",
          "secondary_button_hover_border": "#ffffff",
          "input_background": "rgba(0,0,0,0)",
          "input_text_color": "#00000087",
          "input_border_color": "#00000021",
          "input_hover_background": "#ffffff5c",
          "variant_background_color": "#ffffff",
          "variant_text_color": "#000000",
          "variant_border_color": "#e6e6e6",
          "variant_hover_background_color": "#f5f5f5",
          "variant_hover_text_color": "#000000",
          "variant_hover_border_color": "#e6e6e6",
          "selected_variant_background_color": "#000000",
          "selected_variant_text_color": "#ffffff",
          "selected_variant_border_color": "#000000",
          "selected_variant_hover_background_color": "#1a1a1a",
          "selected_variant_hover_text_color": "#ffffff",
          "selected_variant_hover_border_color": "#1a1a1a"
        }
      },
      "scheme-3": {
        "settings": {
          "background": "#eef1ea",
          "foreground_heading": "#000000",
          "foreground": "#000000cf",
          "primary": "#000000cf",
          "primary_hover": "#000000",
          "border": "#000000cf",
          "shadow": "#000000",
          "primary_button_background": "#000000",
          "primary_button_text": "#ffffff",
          "primary_button_border": "#000000",
          "primary_button_hover_background": "#333333",
          "primary_button_hover_text": "#ffffff",
          "primary_button_hover_border": "#333333",
          "secondary_button_background": "#ffffffb0",
          "secondary_button_text": "#000000",
          "secondary_button_border": "#ffffffb0",
          "secondary_button_hover_background": "#ffffff",
          "secondary_button_hover_text": "#000000",
          "secondary_button_hover_border": "#ffffff",
          "input_background": "rgba(0,0,0,0)",
          "input_text_color": "#000000cf",
          "input_border_color": "#000000cf",
          "input_hover_background": "#ffffff5c",
          "variant_background_color": "#ffffff",
          "variant_text_color": "#000000",
          "variant_border_color": "#e6e6e6",
          "variant_hover_background_color": "#f5f5f5",
          "variant_hover_text_color": "#000000",
          "variant_hover_border_color": "#e6e6e6",
          "selected_variant_background_color": "#000000",
          "selected_variant_text_color": "#ffffff",
          "selected_variant_border_color": "#000000",
          "selected_variant_hover_background_color": "#1a1a1a",
          "selected_variant_hover_text_color": "#ffffff",
          "selected_variant_hover_border_color": "#1a1a1a"
        }
      },
      "scheme-4": {
        "settings": {
          "background": "#e1edf5",
          "foreground_heading": "#000000",
          "foreground": "#000000cf",
          "primary": "#000000cf",
          "primary_hover": "#000000",
          "border": "#1d368680",
          "shadow": "#000000",
          "primary_button_background": "#000000",
          "primary_button_text": "#ffffff",
          "primary_button_border": "#1d3686",
          "primary_button_hover_background": "#333333",
          "primary_button_hover_text": "#ffffff",
          "primary_button_hover_border": "#000000",
          "secondary_button_background": "#ffffffb0",
          "secondary_button_text": "#000000",
          "secondary_button_border": "#000000cf",
          "secondary_button_hover_background": "#ffffff",
          "secondary_button_hover_text": "#000000",
          "secondary_button_hover_border": "#ffffff",
          "input_background": "rgba(0,0,0,0)",
          "input_text_color": "#000000cf",
          "input_border_color": "#000000cf",
          "input_hover_background": "#ffffff5c",
          "variant_background_color": "#ffffff",
          "variant_text_color": "#000000",
          "variant_border_color": "#e6e6e6",
          "variant_hover_background_color": "#f5f5f5",
          "variant_hover_text_color": "#000000",
          "variant_hover_border_color": "#e6e6e6",
          "selected_variant_background_color": "#000000",
          "selected_variant_text_color": "#ffffff",
          "selected_variant_border_color": "#000000",
          "selected_variant_hover_background_color": "#1a1a1a",
          "selected_variant_hover_text_color": "#ffffff",
          "selected_variant_hover_border_color": "#1a1a1a"
        }
      },
      "scheme-5": {
        "settings": {
          "background": "#333333",
          "foreground_heading": "#ffffff",
          "foreground": "#ffffff",
          "primary": "#ffffff",
          "primary_hover": "#ffffffb0",
          "border": "#ffffffb0",
          "shadow": "#000000",
          "primary_button_background": "#ffffff",
          "primary_button_text": "#000000",
          "primary_button_border": "#ffffff",
          "primary_button_hover_background": "#000000",
          "primary_button_hover_text": "#ffffff",
          "primary_button_hover_border": "#000000",
          "secondary_button_background": "#ffffffb0",
          "secondary_button_text": "#000000",
          "secondary_button_border": "#ffffffb0",
          "secondary_button_hover_background": "#ffffff",
          "secondary_button_hover_text": "#000000",
          "secondary_button_hover_border": "#ffffff",
          "input_background": "#333333",
          "input_text_color": "#ffffffed",
          "input_border_color": "#ffffffb0",
          "input_hover_background": "#ffffff0a",
          "variant_background_color": "#ffffff",
          "variant_text_color": "#000000",
          "variant_border_color": "#e6e6e6",
          "variant_hover_background_color": "#f5f5f5",
          "variant_hover_text_color": "#000000",
          "variant_hover_border_color": "#e6e6e6",
          "selected_variant_background_color": "#000000",
          "selected_variant_text_color": "#ffffff",
          "selected_variant_border_color": "#000000",
          "selected_variant_hover_background_color": "#1a1a1a",
          "selected_variant_hover_text_color": "#ffffff",
          "selected_variant_hover_border_color": "#1a1a1a"
        }
      },
      "scheme-6": {
        "settings": {
          "background": "rgba(0,0,0,0)",
          "foreground_heading": "#ffffff",
          "foreground": "#ffffff",
          "primary": "#ffffff",
          "primary_hover": "#ffffffb0",
          "border": "#e6e6e6",
          "shadow": "#000000",
          "primary_button_background": "#ffffff",
          "primary_button_text": "#000000",
          "primary_button_border": "#ffffff",
          "primary_button_hover_background": "#000000",
          "primary_button_hover_text": "#ffffff",
          "primary_button_hover_border": "#000000",
          "secondary_button_background": "#ffffffb0",
          "secondary_button_text": "#000000",
          "secondary_button_border": "#ffffffb0",
          "secondary_button_hover_background": "#ffffff",
          "secondary_button_hover_text": "#000000",
          "secondary_button_hover_border": "#ffffff",
          "input_background": "#ffffff",
          "input_text_color": "#00000087",
          "input_border_color": "#00000021",
          "input_hover_background": "#fafafa",
          "variant_background_color": "#ffffff",
          "variant_text_color": "#000000",
          "variant_border_color": "#e6e6e6",
          "variant_hover_background_color": "#f5f5f5",
          "variant_hover_text_color": "#000000",
          "variant_hover_border_color": "#e6e6e6",
          "selected_variant_background_color": "#000000",
          "selected_variant_text_color": "#ffffff",
          "selected_variant_border_color": "#000000",
          "selected_variant_hover_background_color": "#1a1a1a",
          "selected_variant_hover_text_color": "#ffffff",
          "selected_variant_hover_border_color": "#1a1a1a"
        }
      },
      "scheme-58084d4c-a86e-4d0a-855e-a0966e5043f7": {
        "settings": {
          "background": "rgba(0,0,0,0)",
          "foreground_heading": "#000000",
          "foreground": "#000000",
          "primary": "#000000",
          "primary_hover": "#00000087",
          "border": "#e6e6e6",
          "shadow": "#000000",
          "primary_button_background": "#000000",
          "primary_button_text": "#ffffff",
          "primary_button_border": "#000000",
          "primary_button_hover_background": "#333333",
          "primary_button_hover_text": "#ffffff",
          "primary_button_hover_border": "#333333",
          "secondary_button_background": "#0000000f",
          "secondary_button_text": "#000000",
          "secondary_button_border": "#000000",
          "secondary_button_hover_background": "#ffffff",
          "secondary_button_hover_text": "#000000",
          "secondary_button_hover_border": "#ffffff",
          "input_background": "#ffffff",
          "input_text_color": "#00000087",
          "input_border_color": "#00000021",
          "input_hover_background": "#fafafa",
          "variant_background_color": "#ffffff",
          "variant_text_color": "#000000",
          "variant_border_color": "#e6e6e6",
          "variant_hover_background_color": "#f5f5f5",
          "variant_hover_text_color": "#000000",
          "variant_hover_border_color": "#e6e6e6",
          "selected_variant_background_color": "#000000",
          "selected_variant_text_color": "#ffffff",
          "selected_variant_border_color": "#000000",
          "selected_variant_hover_background_color": "#1a1a1a",
          "selected_variant_hover_text_color": "#ffffff",
          "selected_variant_hover_border_color": "#1a1a1a"
        }
      }
    }
  },
  "presets": {
    "Horizon": {
      "color_schemes": {
        "scheme-1": {
          "settings": {
            "background": "#ffffff",
            "foreground_heading": "#000000",
            "foreground": "#000000cf",
            "primary": "#000000cf",
            "primary_hover": "#000000",
            "border": "#0000000f",
            "shadow": "#000000",
            "primary_button_background": "#000000",
            "primary_button_text": "#ffffff",
            "primary_button_border": "#000000",
            "primary_button_hover_background": "#333333",
            "primary_button_hover_text": "#ffffff",
            "primary_button_hover_border": "#000000",
            "secondary_button_background": "#0000000f",
            "secondary_button_text": "#000000",
            "secondary_button_border": "#0000000f",
            "secondary_button_hover_background": "#333333",
            "secondary_button_hover_text": "#ffffff",
            "secondary_button_hover_border": "#333333",
            "input_background": "#ffffffc7",
            "input_text_color": "#333333",
            "input_border_color": "#f5f5f5",
            "input_hover_background": "#00000003",
            "variant_background_color": "#ffffff",
            "variant_text_color": "#000000",
            "variant_border_color": "#00000021",
            "variant_hover_background_color": "#f5f5f5",
            "variant_hover_text_color": "#000000",
            "variant_hover_border_color": "#e6e6e6",
            "selected_variant_background_color": "#000000",
            "selected_variant_text_color": "#ffffff",
            "selected_variant_border_color": "#000000",
            "selected_variant_hover_background_color": "#1a1a1a",
            "selected_variant_hover_text_color": "#ffffff",
            "selected_variant_hover_border_color": "#1a1a1a"
          }
        },
        "scheme-2": {
          "settings": {
            "background": "#f5f5f5",
            "foreground_heading": "#000000",
            "foreground": "#000000cf",
            "primary": "#000000cf",
            "primary_hover": "#000000",
            "border": "#DFDFDF",
            "shadow": "#000000",
            "primary_button_background": "#000000",
            "primary_button_text": "#ffffff",
            "primary_button_border": "#000000",
            "primary_button_hover_background": "#333333",
            "primary_button_hover_text": "#ffffff",
            "primary_button_hover_border": "#333333",
            "secondary_button_background": "#ffffffb0",
            "secondary_button_text": "#000000",
            "secondary_button_border": "#ffffffb0",
            "secondary_button_hover_background": "#ffffff",
            "secondary_button_hover_text": "#000000",
            "secondary_button_hover_border": "#ffffff",
            "input_background": "rgba(0,0,0,0)",
            "input_text_color": "#00000087",
            "input_border_color": "#00000021",
            "input_hover_background": "#ffffff5c",
            "variant_background_color": "#ffffff",
            "variant_text_color": "#000000",
            "variant_border_color": "#e6e6e6",
            "variant_hover_background_color": "#f5f5f5",
            "variant_hover_text_color": "#000000",
            "variant_hover_border_color": "#e6e6e6",
            "selected_variant_background_color": "#000000",
            "selected_variant_text_color": "#ffffff",
            "selected_variant_border_color": "#000000",
            "selected_variant_hover_background_color": "#1a1a1a",
            "selected_variant_hover_text_color": "#ffffff",
            "selected_variant_hover_border_color": "#1a1a1a"
          }
        },
        "scheme-3": {
          "settings": {
            "background": "#eef1ea",
            "foreground_heading": "#000000",
            "foreground": "#000000cf",
            "primary": "#000000cf",
            "primary_hover": "#000000",
            "border": "#000000cf",
            "shadow": "#000000",
            "primary_button_background": "#000000",
            "primary_button_text": "#ffffff",
            "primary_button_border": "#000000",
            "primary_button_hover_background": "#333333",
            "primary_button_hover_text": "#ffffff",
            "primary_button_hover_border": "#333333",
            "secondary_button_background": "#ffffffb0",
            "secondary_button_text": "#000000",
            "secondary_button_border": "#ffffffb0",
            "secondary_button_hover_background": "#ffffff",
            "secondary_button_hover_text": "#000000",
            "secondary_button_hover_border": "#ffffff",
            "input_background": "rgba(0,0,0,0)",
            "input_text_color": "#000000cf",
            "input_border_color": "#000000cf",
            "input_hover_background": "#ffffff5c",
            "variant_background_color": "#ffffff",
            "variant_text_color": "#000000",
            "variant_border_color": "#e6e6e6",
            "variant_hover_background_color": "#f5f5f5",
            "variant_hover_text_color": "#000000",
            "variant_hover_border_color": "#e6e6e6",
            "selected_variant_background_color": "#000000",
            "selected_variant_text_color": "#ffffff",
            "selected_variant_border_color": "#000000",
            "selected_variant_hover_background_color": "#1a1a1a",
            "selected_variant_hover_text_color": "#ffffff",
            "selected_variant_hover_border_color": "#1a1a1a"
          }
        },
        "scheme-4": {
          "settings": {
            "background": "#e1edf5",
            "foreground_heading": "#000000",
            "foreground": "#000000cf",
            "primary": "#000000cf",
            "primary_hover": "#000000",
            "border": "#1d368680",
            "shadow": "#000000",
            "primary_button_background": "#000000",
            "primary_button_text": "#ffffff",
            "primary_button_border": "#1d3686",
            "primary_button_hover_background": "#333333",
            "primary_button_hover_text": "#ffffff",
            "primary_button_hover_border": "#000000",
            "secondary_button_background": "#ffffffb0",
            "secondary_button_text": "#000000",
            "secondary_button_border": "#000000cf",
            "secondary_button_hover_background": "#ffffff",
            "secondary_button_hover_text": "#000000",
            "secondary_button_hover_border": "#ffffff",
            "input_background": "rgba(0,0,0,0)",
            "input_text_color": "#000000cf",
            "input_border_color": "#000000cf",
            "input_hover_background": "#ffffff5c",
            "variant_background_color": "#ffffff",
            "variant_text_color": "#000000",
            "variant_border_color": "#e6e6e6",
            "variant_hover_background_color": "#f5f5f5",
            "variant_hover_text_color": "#000000",
            "variant_hover_border_color": "#e6e6e6",
            "selected_variant_background_color": "#000000",
            "selected_variant_text_color": "#ffffff",
            "selected_variant_border_color": "#000000",
            "selected_variant_hover_background_color": "#1a1a1a",
            "selected_variant_hover_text_color": "#ffffff",
            "selected_variant_hover_border_color": "#1a1a1a"
          }
        },
        "scheme-5": {
          "settings": {
            "background": "#333333",
            "foreground_heading": "#ffffff",
            "foreground": "#ffffff",
            "primary": "#ffffff",
            "primary_hover": "#ffffffb0",
            "border": "#ffffffb0",
            "shadow": "#000000",
            "primary_button_background": "#ffffff",
            "primary_button_text": "#000000",
            "primary_button_border": "#ffffff",
            "primary_button_hover_background": "#000000",
            "primary_button_hover_text": "#ffffff",
            "primary_button_hover_border": "#000000",
            "secondary_button_background": "#ffffffb0",
            "secondary_button_text": "#000000",
            "secondary_button_border": "#ffffffb0",
            "secondary_button_hover_background": "#ffffff",
            "secondary_button_hover_text": "#000000",
            "secondary_button_hover_border": "#ffffff",
            "input_background": "#333333",
            "input_text_color": "#ffffffed",
            "input_border_color": "#ffffffb0",
            "input_hover_background": "#ffffff0a",
            "variant_background_color": "#ffffff",
            "variant_text_color": "#000000",
            "variant_border_color": "#e6e6e6",
            "variant_hover_background_color": "#f5f5f5",
            "variant_hover_text_color": "#000000",
            "variant_hover_border_color": "#e6e6e6",
            "selected_variant_background_color": "#000000",
            "selected_variant_text_color": "#ffffff",
            "selected_variant_border_color": "#000000",
            "selected_variant_hover_background_color": "#1a1a1a",
            "selected_variant_hover_text_color": "#ffffff",
            "selected_variant_hover_border_color": "#1a1a1a"
          }
        },
        "scheme-6": {
          "settings": {
            "background": "rgba(0,0,0,0)",
            "foreground_heading": "#ffffff",
            "foreground": "#ffffff",
            "primary": "#ffffff",
            "primary_hover": "#ffffffb0",
            "border": "#e6e6e6",
            "shadow": "#000000",
            "primary_button_background": "#ffffff",
            "primary_button_text": "#000000",
            "primary_button_border": "#ffffff",
            "primary_button_hover_background": "#000000",
            "primary_button_hover_text": "#ffffff",
            "primary_button_hover_border": "#000000",
            "secondary_button_background": "#ffffffb0",
            "secondary_button_text": "#000000",
            "secondary_button_border": "#ffffffb0",
            "secondary_button_hover_background": "#ffffff",
            "secondary_button_hover_text": "#000000",
            "secondary_button_hover_border": "#ffffff",
            "input_background": "#ffffff",
            "input_text_color": "#00000087",
            "input_border_color": "#00000021",
            "input_hover_background": "#fafafa",
            "variant_background_color": "#ffffff",
            "variant_text_color": "#000000",
            "variant_border_color": "#e6e6e6",
            "variant_hover_background_color": "#f5f5f5",
            "variant_hover_text_color": "#000000",
            "variant_hover_border_color": "#e6e6e6",
            "selected_variant_background_color": "#000000",
            "selected_variant_text_color": "#ffffff",
            "selected_variant_border_color": "#000000",
            "selected_variant_hover_background_color": "#1a1a1a",
            "selected_variant_hover_text_color": "#ffffff",
            "selected_variant_hover_border_color": "#1a1a1a"
          }
        },
        "scheme-58084d4c-a86e-4d0a-855e-a0966e5043f7": {
          "settings": {
            "background": "rgba(0,0,0,0)",
            "foreground_heading": "#000000",
            "foreground": "#000000",
            "primary": "#000000",
            "primary_hover": "#00000087",
            "border": "#e6e6e6",
            "shadow": "#000000",
            "primary_button_background": "#000000",
            "primary_button_text": "#ffffff",
            "primary_button_border": "#000000",
            "primary_button_hover_background": "#333333",
            "primary_button_hover_text": "#ffffff",
            "primary_button_hover_border": "#333333",
            "secondary_button_background": "#0000000f",
            "secondary_button_text": "#000000",
            "secondary_button_border": "#000000",
            "secondary_button_hover_background": "#ffffff",
            "secondary_button_hover_text": "#000000",
            "secondary_button_hover_border": "#ffffff",
            "input_background": "#ffffff",
            "input_text_color": "#00000087",
            "input_border_color": "#00000021",
            "input_hover_background": "#fafafa",
            "variant_background_color": "#ffffff",
            "variant_text_color": "#000000",
            "variant_border_color": "#e6e6e6",
            "variant_hover_background_color": "#f5f5f5",
            "variant_hover_text_color": "#000000",
            "variant_hover_border_color": "#e6e6e6",
            "selected_variant_background_color": "#000000",
            "selected_variant_text_color": "#ffffff",
            "selected_variant_border_color": "#000000",
            "selected_variant_hover_background_color": "#1a1a1a",
            "selected_variant_hover_text_color": "#ffffff",
            "selected_variant_hover_border_color": "#1a1a1a"
          }
        }
      }
    }
  }
}
