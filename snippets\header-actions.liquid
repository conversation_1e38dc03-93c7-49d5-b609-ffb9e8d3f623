<script
  type="module"
  src="{{ 'cart-icon.js' | asset_url }}"
></script>

<header-actions
  {{- block.shopify_attributes -}}
>
  {% if shop.customer_accounts_enabled %}
    {% render 'account-popover' %}
    {% render 'account-drawer' %}
  {% endif %}

  {% if settings.cart_type == 'drawer' and template.name != 'cart' %}
    {% render 'cart-drawer' %}
  {% else %}
    <a
      href="{{ routes.cart_url }}"
      class="header-actions__action action__cart"
      aria-label="{{ 'accessibility.cart' | t }} {{ 'accessibility.cart_count' | t }}: {{ cart.item_count }}"
    >
      {% render 'cart-icon-component' %}
    </a>
  {% endif %}
</header-actions>

{% stylesheet %}
  .cart-drawer {
    --cart-drawer-padding: var(--padding-lg) var(--padding-xl);
    --cart-drawer-padding-desktop: var(--padding-xl) var(--padding-2xl);
    --cart-font-size--2xs: var(--font-size--2xs);
    --cart-font-size--xs: var(--font-size--xs);
    --cart-font-size--sm: var(--font-size--sm);
    --cart-font-size--md: var(--font-size--md);
    --cart-font-size--2xl: var(--font-size--2xl);
  }

  .cart-drawer__dialog {
    position: fixed;
    border-radius: 0;
    width: var(--sidebar-width);
    max-width: 95vw;
    height: 100%;
    margin: 0 0 0 auto;
    padding: 0;
    border-left: var(--style-border-drawer);
    box-shadow: var(--shadow-drawer);
    background-color: var(--color-background);
  }

  /* Needed to ensure the drawer is full height */
  .cart-drawer__dialog:modal {
    max-height: 100dvh;
    overflow-y: hidden;
  }

  .cart-drawer__inner {
    height: 100%;
  }

  .cart-drawer__content {
    padding: 0;
    background-color: var(--color-background);
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    overflow-y: auto;
  }

  .cart-drawer__heading {
    display: flex;
    align-items: center;
    gap: var(--gap-xs);
  }

  .cart-drawer__close-button {
    width: var(--minimum-touch-target);
    height: var(--minimum-touch-target);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: calc(var(--padding-sm) * -1);
  }

  .cart-drawer__close-button svg {
    width: var(--icon-size-xs);
    height: var(--icon-size-xs);
  }

  .cart-drawer--empty .cart-drawer__content {
    text-align: center;
    min-height: auto;
  }

  .cart-drawer--empty .cart-drawer__heading {
    margin-bottom: var(--margin-md);
  }

  .cart-drawer__items .cart-items__table-row {
    padding-bottom: var(--gap-xl);
    border-bottom: var(--style-border-width) solid var(--color-border);
    margin-bottom: var(--gap-xl);
  }

  .cart-drawer__items .cart-items__table-row:last-child {
    border-bottom: none;
  }

  .cart-drawer__summary {
    --cart-drawer-summary-padding: var(--padding-lg);

    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--gap-2xl);
    padding: var(--cart-drawer-summary-padding);
    margin-top: auto;
    background-color: var(--color-background);
    /* stylelint-disable-next-line color-named */
    mask-image: linear-gradient(to bottom, transparent, black var(--cart-drawer-summary-padding));

    @media screen and (min-width: 750px) {
      --cart-drawer-summary-padding: var(--padding-2xl);
    }
  }

  .cart-drawer__summary .cart__summary-totals:not(:has(.cart__original-total-container:empty)) {
    border-block-start: var(--style-border-width) solid var(--color-border);
    padding-block-start: var(--padding-2xl);
  }

  .cart-drawer__summary .cart-note {
    @media screen and (min-width: 750px) {
      margin-block-start: var(--margin-3xs);
    }
  }

  .cart-drawer__heading--empty {
    display: flex;
    justify-content: center;
  }

  .cart-drawer__items {
    display: flex;
    flex-direction: column;
    padding-inline: var(--cart-drawer-padding);
    overflow-y: auto;

    @media screen and (min-width: 750px) {
      padding-inline: var(--cart-drawer-padding-desktop);
    }
  }

  .cart-drawer__items .cart-items__table-row {
    padding-bottom: var(--gap-xl);
    border-bottom: var(--style-border-width) solid var(--color-border);
    margin-bottom: var(--gap-xl);
  }

  .cart-drawer__items .cart-items__table-row:last-child {
    border-bottom: none;
    padding-block-end: 0;
    margin-block-end: 0;
  }

  .cart-drawer--empty .cart-drawer__inner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100dvh;
    margin-top: 0;
  }

  .cart-drawer:not(:has(.cart-form)) .cart-drawer__content {
    justify-content: center;
  }

  .cart-drawer--empty .cart-drawer__header {
    justify-content: right;
    border-bottom: none;
    padding-bottom: 0;
  }

  .cart-drawer--empty .cart-drawer__heading {
    text-align: center;
  }

  .cart-drawer:not(:has(.cart-form)) .cart-items__wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
  }

  header-actions {
    display: flex;

    @media screen and (max-width: 749px) {
      justify-self: flex-end;
    }
  }

  .header__column--right header-actions {
    margin-inline-start: calc(var(--gap-md) * -1);
  }

  .header-actions__action {
    cursor: pointer;
    display: flex;
    justify-content: center;

    --button-color: var(--color-foreground);
  }

  .header-actions__action .svg-wrapper {
    height: var(--button-size);
    width: var(--button-size);
  }

  .header-actions__action svg {
    width: var(--icon-size-md);
    height: var(--icon-size-md);
  }

  .header-actions__cart-icon {
    position: relative;
    --cart-bubble-size: 20px;
    --cart-bubble-top: 4.5px;
    --cart-bubble-right: 2.5px;
  }
  .header-actions__cart-icon .cart-bubble {
    position: absolute;
    width: var(--cart-bubble-size, 20px);
    top: var(--cart-bubble-top);
    right: var(--cart-bubble-right);
  }

  .header-actions__cart-icon .cart-bubble__text,
  .cart-drawer__heading .cart-bubble__text {
    font-family: var(--font-paragraph--family);
    font-weight: var(--font-paragraph--weight);
  }

  .header-actions__cart-icon.header-actions__cart-icon--has-cart svg {
    /* Create donut mask where the cart bubble sits */
    mask: radial-gradient(
      calc(var(--cart-bubble-size) + 2px) at calc(100% - var(--cart-bubble-right)) var(--cart-bubble-top),
      transparent 45.45%,
      white 45.45%,
      white 100%
    );
  }

  .cart-drawer__heading .cart-bubble__background {
    background-color: rgb(from var(--color-foreground) r g b / var(--opacity-10-25));
  }

  .cart-drawer__heading .cart-bubble__text {
    color: var(--color-foreground);
    font-size: var(--font-size--xs);
  }

  .cart-bubble--animating .cart-bubble__background {
    animation: grow var(--animation-speed) var(--animation-easing);
  }

  .cart-bubble--animating .cart-bubble__text {
    animation: cartBubbleSlideIn var(--animation-speed) var(--animation-easing);
  }
{% endstylesheet %}
