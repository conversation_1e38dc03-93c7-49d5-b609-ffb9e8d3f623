/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin language editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "names": {
    "404": "404",
    "borders": "Ränder",
    "collapsible_row": "Einklappbare Reihe",
    "custom_section": "Benutzerdefinierter Abschnitt",
    "icon": "Symbol",
    "logo_and_favicon": "Logo und Favicon",
    "product_buy_buttons": "Kaufschaltflächen",
    "product_description": "Beschreibung",
    "product_price": "Preis",
    "slideshow": "Slideshow",
    "typography": "Typografie",
    "video": "Video",
    "colors": "Farben",
    "overlapping_blocks": "Überlappende Blöcke",
    "product_variant_picker": "Variantenauswahl",
    "slideshow_controls": "Slideshow-Steuerelemente",
    "size": "Größe",
    "spacing": "Abstand",
    "product_recommendations": "Empfohlene Produkte",
    "product_media": "Produktmedien",
    "featured_collection": "Vorgestellte Kollektion",
    "add_to_cart": "In den Warenkorb legen",
    "email_signup": "E-Mail-Anmeldung",
    "submit_button": "Schaltfläche „Senden“",
    "grid_layout_selector": "Raster-Layout-Auswahl",
    "image": "Bild",
    "list_items": "Listenelemente",
    "facets": "Filter",
    "variants": "Varianten",
    "styles": "Stile",
    "product_cards": "Produktkarten",
    "buttons": "Schaltflächen",
    "inputs": "Eingaben",
    "primary_button": "Primäre Schaltfläche",
    "secondary_button": "Sekundäre Schaltfläche",
    "popovers": "Pop-overs",
    "marquee": "Marquee",
    "products_carousel": "Produktliste: Karussell",
    "products_grid": "Produktliste: Raster",
    "pull_quote": "Zitat",
    "contact_form": "Kontaktformular",
    "featured_product": "Produkthighlight",
    "icons_with_text": "Symbole mit Text",
    "alternating_content_rows": "Abwechselnde Zeilen",
    "accelerated_checkout": "Beschleunigter Checkout",
    "accordion": "Akkordeon",
    "accordion_row": "Akkordeon-Zeile",
    "animations": "Animationen",
    "announcement": "Ankündigung",
    "announcement_bar": "Ankündigungsleiste",
    "badges": "Badges",
    "button": "Schaltfläche",
    "cart": "Warenkorb",
    "cart_items": "Artikel im Warenkorb",
    "cart_products": "Produkte im Warenkorb",
    "cart_title": "Warenkorb",
    "collection": "Kollektion",
    "collection_card": "Kollektionskarte",
    "collection_columns": "Kollektionsspalten",
    "collection_container": "Kollektion",
    "collection_description": "Kollektionsbeschreibung",
    "collection_image": "Kollektionsbild",
    "collection_info": "Kollektionsinformationen",
    "collection_list": "Kollektionsliste",
    "collections": "Kollektionen",
    "collections_bento": "Kollektionsliste: Bento",
    "collections_carousel": "Kollektionsliste: Karussell",
    "collections_grid": "Kollektionsliste: Raster",
    "content": "Inhalt",
    "content_grid": "Inhaltsraster",
    "details": "Details",
    "divider": "Trennlinie",
    "divider_section": "Trennlinie",
    "faq_section": "FAQ",
    "filters": "Filtern und Sortieren",
    "follow_on_shop": "In Shop folgen",
    "footer": "Fußzeile",
    "footer_utilities": "Tools für Fußzeile",
    "group": "Gruppe",
    "header": "Header",
    "heading": "Überschrift",
    "hero": "Hero-Bild",
    "icons": "Symbole",
    "image_with_text": "Bild mit Text",
    "input": "Eingabe",
    "logo": "Logo",
    "magazine_grid": "Magazinraster",
    "media": "Medien",
    "menu": "Menü",
    "mobile_layout": "Mobiles Layout",
    "payment_icons": "Zahlungssymbole",
    "popup_link": "Pop-up-Link",
    "predictive_search": "Popover suchen",
    "predictive_search_empty": "Vorausschauende Suche leer",
    "price": "Preis",
    "product": "Produkt",
    "product_card": "Produktkarte",
    "product_card_media": "Medien",
    "product_card_rendering": "Rendering der Produktkarten",
    "product_grid": "Raster",
    "product_grid_main": "Produktraster",
    "product_image": "Produktbild",
    "product_information": "Produktinformationen",
    "product_review_stars": "Bewertungssterne",
    "quantity": "Anzahl",
    "row": "Zeile",
    "search": "Suche",
    "section": "Abschnitt",
    "selected_variants": "Ausgewählte Varianten",
    "shop_the_look": "Look kaufen",
    "slide": "Folie",
    "social_media_links": "Social-Media-Links",
    "steps": "Schritte",
    "summary": "Übersicht",
    "swatches": "Farbfelder",
    "testimonials": "Erfahrungsberichte",
    "text": "Text",
    "title": "Titel",
    "utilities": "Hilfsprogramme",
    "video_section": "Video",
    "spacer": "Abstandhalter",
    "product_list": "Produktliste",
    "jumbo_text": "Text im Großformat",
    "search_input": "Sucheingabe",
    "search_results": "Suchergebnisse",
    "read_only": "Schreibgeschützt",
    "collection_title": "Kollektionstitel",
    "view_all_button": "Alle anzeigen",
    "custom_liquid": "Benutzerdefiniertes Liquid",
    "blog": "Blog",
    "blog_post": "Blog-Beitrag",
    "blog_posts": "Blog-Beiträge",
    "caption": "Bildtext",
    "collection_card_image": "Bild",
    "collection_links": "Kollektions-Links",
    "collection_links_spotlight": "Kollektionslinks: Spotlight",
    "collection_links_text": "Kollektionslinks: Text",
    "collections_editorial": "Kollektionsliste: Editorial",
    "copyright": "Urheberrecht",
    "count": "Anzahl",
    "drawers": "Einschübe",
    "editorial": "Editorial",
    "editorial_jumbo_text": "Editorial: Text im Großformat",
    "hero_marquee": "Hero: Marquee",
    "input_fields": "Eingabefelder",
    "local_pickup": "Lokale Abholung",
    "marquee_section": "Marquee",
    "media_with_text": "Medien mit Text",
    "page": "Seite",
    "page_content": "Inhalt",
    "page_layout": "Seitenlayout",
    "policy_list": "Links zu Richtlinien",
    "prices": "Preise",
    "products_editorial": "Produktliste: Editorial",
    "social_link": "Social-Media-Link",
    "split_showcase": "Split Showcase",
    "variant_pickers": "Variantenauswahl",
    "product_title": "Produkttitel",
    "large_logo": "Großes Logo",
    "product_list_button": "Schaltfläche „Alle anzeigen“",
    "product_inventory": "Produktinventar",
    "pills": "Kapseln",
    "description": "Beschreibung"
  },
  "settings": {
    "autoplay": "Automatische Wiedergabe",
    "background": "Hintergrund",
    "border_radius": "Eckradius",
    "border_width": "Randdicke",
    "borders": "Ränder",
    "bottom_padding": "Unteres Padding",
    "color": "Farbe",
    "content_direction": "Inhaltsrichtung",
    "content_position": "Inhaltsposition",
    "cover_image_size": "Größe des Titelbilds",
    "cover_image": "Titelbild",
    "custom_width": "Benutzerdefinierte Breite",
    "enable_video_looping": "Videoschleife",
    "favicon": "Favicon",
    "heading": "Titel",
    "icon": "Symbol",
    "image_icon": "Bildsymbol",
    "make_section_full_width": "Abschnitt über die gesamte Breite",
    "overlay_opacity": "Überlagerungsdeckkraft",
    "padding": "Padding",
    "product": "Produkt",
    "text": "Text",
    "top_padding": "Oberes Padding",
    "video": "Video",
    "video_alt_text": "Alt-Text",
    "video_loop": "Video in Dauerschleife",
    "video_position": "Videoposition",
    "width": "Breite",
    "alignment": "Ausrichtung",
    "button": "Schaltfläche",
    "colors": "Farben",
    "content_alignment": "Inhaltsausrichtung",
    "custom_minimum_height": "Benutzerdefinierte Mindesthöhe",
    "font_family": "Schriftartfamilie",
    "gap": "Abstand",
    "geometric_translate_y": "Geometric translate Y",
    "image": "Bild",
    "image_opacity": "Deckkraft des Bilds",
    "image_position": "Bildposition",
    "image_ratio": "Bildverhältnis",
    "label": "Beschriftung",
    "line_height": "Zeilenhöhe",
    "link": "Link",
    "layout_gap": "Lücke im Layout",
    "minimum_height": "Mindesthöhe",
    "opacity": "Opazität",
    "primary_color": "Links",
    "section_width": "Abschnittsbreite",
    "size": "Größe",
    "slide_spacing": "Lücke in der Folie",
    "slide_width": "Folienbreite",
    "slideshow_fullwidth": "Folien in voller Breite",
    "style": "Optik",
    "text_case": "Fall",
    "z_index": "z-index",
    "limit_content_width": "Begrenzte Inhaltsbreite",
    "color_scheme": "Farbschema",
    "inherit_color_scheme": "Farbschema übernehmen",
    "product_count": "Produktanzahl",
    "product_type": "Produkttyp",
    "content_width": "Inhaltsbreite",
    "collection": "Kollektion",
    "enable_sticky_content": "Fixierte Inhalte für Desktop",
    "error_color": "Fehler",
    "success_color": "Erfolg",
    "primary_font": "Primäre Schriftart",
    "secondary_font": "Sekundäre Schriftart",
    "tertiary_font": "Tertiäre Schriftart",
    "columns": "Spalten",
    "items_to_show": "Anzuzeigende Elemente",
    "layout": "Layout",
    "layout_type": "Art",
    "show_grid_layout_selector": "Raster-Layout-Auswahl anzeigen",
    "view_more_show": "\"Mehr anzeigen\"-Schaltfläche anzeigen",
    "image_gap": "Bildabstand",
    "width_desktop": "Breite bei Desktop-Ansicht",
    "width_mobile": "Breite bei mobiler Ansicht",
    "border_style": "Stil für Rand",
    "height": "Höhe",
    "thickness": "Dicke",
    "stroke": "Strich",
    "filter_style": "Filterstil",
    "swatches": "Farbfelder",
    "quick_add_colors": "Farben schnell hinzufügen",
    "divider_color": "Trennlinie",
    "border_opacity": "Deckkraft des Rands",
    "hover_background": "Hover-Hintergrund",
    "hover_borders": "Hover-Grenzen",
    "hover_text": "Hover-Text",
    "primary_hover_color": "Hover-Links",
    "primary_button_text": "Text für primäre Schaltfläche",
    "primary_button_background": "Hintergrund für primäre Schaltfläche",
    "primary_button_border": "Grenze für primäre Schaltfläche",
    "secondary_button_text": "Text für sekundäre Schaltfläche",
    "secondary_button_background": "Hintergrund für sekundäre Schaltfläche",
    "secondary_button_border": "Grenze für sekundäre Schaltfläche",
    "shadow_color": "Schatten",
    "mobile_logo_image": "Mobiles Logo",
    "video_autoplay": "Automatische Wiedergabe",
    "video_cover_image": "Titelbild",
    "video_external_url": "URL",
    "video_source": "Quelle",
    "card_image_height": "Produktbildhöhe",
    "first_row_media_position": "Erste Zeile Medienposition",
    "accordion": "Akkordeon",
    "aspect_ratio": "Seitenverhältnis",
    "auto_rotate_announcements": "Autorotieren der Ankündigungen",
    "auto_rotate_slides": "Autorotieren der Slides",
    "badge_corner_radius": "Eckradius",
    "badge_position": "Position auf Karten",
    "badge_sale_color_scheme": "Sale",
    "badge_sold_out_color_scheme": "Ausverkauft",
    "behavior": "Verhalten",
    "blur": "Schatten-Weichzeichnung",
    "border": "Rand",
    "bottom": "Unten",
    "carousel_on_mobile": "Karussell in mobiler Ansicht",
    "cart_count": "Anzahl im Warenkorb",
    "cart_items": "Artikel im Warenkorb",
    "cart_related_products": "Ähnliche Produkte",
    "cart_title": "Warenkorb",
    "cart_total": "Gesamtbetrag im Warenkorb",
    "cart_type": "Art",
    "case": "Fall",
    "checkout_buttons": "Schaltflächen für beschleunigten Checkout",
    "collection_list": "Kollektionen",
    "collection_templates": "Kollektionsvorlagen",
    "content": "Inhalt",
    "corner_radius": "Eckradius",
    "country_region": "Land/Region",
    "currency_code": "Währungscode",
    "custom_height": "Benutzerdefinierte Höhe",
    "desktop_height": "Desktop-Höhe",
    "direction": "Richtung",
    "display": "Anzeige",
    "divider_thickness": "Dicke der Trennlinie",
    "divider": "Trennlinie",
    "dividers": "Trennlinien",
    "drop_shadow": "Schlagschatten",
    "empty_state_collection_info": "Wird vor der Eingabe einer Suche angezeigt",
    "empty_state_collection": "Kollektion mit Status „Leer“",
    "enable_filtering": "Filter",
    "enable_grid_density": "Steuerung des Raster-Layouts",
    "enable_sorting": "Sortierung",
    "enable_zoom": "Zoom aktivieren",
    "equal_columns": "Gleiche Spalten",
    "expand_first_group": "Erste Gruppe erweitern",
    "extend_media_to_screen_edge": "Medien bis zum Bildschirmrand erweitern",
    "extend_summary": "Bis zum Bildschirmrand erweitern",
    "extra_large": "Extra groß",
    "extra_small": "Extra klein",
    "flag": "Markierung",
    "font_price": "Schriftart für Preis",
    "font_weight": "Schriftstärke",
    "font": "Schriftart",
    "full_width_first_image": "Erstes Bild in voller Breite",
    "full_width_on_mobile": "Volle Breite in mobiler Ansicht",
    "heading_preset": "Überschrift (Voreinstellung)",
    "hide_unselected_variant_media": "Medien für nicht ausgewählte Variante ausblenden",
    "horizontal_gap": "Horizontale Lücke",
    "horizontal_offset": "Horizontaler Offset für Schatten",
    "hover_behavior": "Hover-Verhalten",
    "icon_background": "Symbol-Hintergrund",
    "icons": "Symbole",
    "image_border_radius": "Eckradius von Bild",
    "installments": "Raten",
    "integrated_button": "Integrierte Schaltfläche",
    "language_selector": "Sprachauswahl",
    "large": "Groß",
    "left_padding": "Padding links",
    "left": "Links",
    "letter_spacing": "Buchstabenabstand",
    "limit_media_to_screen_height": "Auf Bildschirmhöhe beschränken",
    "limit_product_details_width": "Breite der Produktdetails beschränken",
    "link_preset": "Link-Voreinstellung",
    "links": "Links",
    "logo": "Logo",
    "loop": "Schleife",
    "make_details_sticky_desktop": "Bei Desktop fixiert",
    "max_width": "Maximale Breite",
    "media_height": "Medienhöhe",
    "media_overlay": "Medienüberlagerung",
    "media_position": "Medienposition",
    "media_type": "Medientyp",
    "media_width": "Medienbreite",
    "menu": "Menü",
    "mobile_columns": "Spalten für mobile Ansicht",
    "mobile_height": "Höhe für mobile Ansicht",
    "mobile_quick_add": "Schnelles Hinzufügen für mobile Ansicht",
    "motion_direction": "Bewegungsrichtung",
    "motion": "Bewegung",
    "movement_direction": "Bewegungsrichtung",
    "navigation_bar_color_scheme": "Farbschema für Navigationsleiste",
    "navigation_bar": "Navigationsleiste",
    "navigation": "Navigation",
    "open_new_tab": "Link in neuem Tab öffnen",
    "overlay_color": "Überlagerungsfarbe",
    "overlay": "Überlagerung",
    "padding_bottom": "Padding unten",
    "padding_horizontal": "Padding horizontal",
    "padding_top": "Padding oben",
    "page_width": "Seitenbreite",
    "pagination": "Seitennummerierung",
    "placement": "Platzierung",
    "position": "Position",
    "preset": "Voreinstellung",
    "product_cards": "Produktkarten",
    "product_pages": "Produktseiten",
    "product_templates": "Produktvorlagen",
    "products": "Produkte",
    "quick_add": "Schnelles Hinzufügen",
    "ratio": "Verhältnis",
    "regular": "Regulär",
    "review_count": "Anzahl überprüfen",
    "right": "Rechts",
    "row_height": "Zeilenhöhe",
    "row": "Zeile",
    "seller_note": "Notiz für Verkäufer zulassen",
    "shape": "Form",
    "show_as_accordion": "Als Akkordeon in mobiler Ansicht anzeigen",
    "show_sale_price_first": "Verkaufspreis zuerst anzeigen",
    "show_tax_info": "Steuerinformationen",
    "show": "Anzeigen",
    "small": "Klein",
    "speed": "Geschwindigkeit",
    "statement": "Aussage",
    "sticky_header": "Fixierter Header",
    "text_hierarchy": "Texthierarchie",
    "text_presets": "Textvoreinstellungen",
    "title": "Titel",
    "top": "Oben",
    "type_preset": "Textvoreinstellung",
    "type": "Art",
    "underline_thickness": "Unterstreichungsstärke",
    "variant_images": "Variantenbilder",
    "vendor": "Anbieter",
    "vertical_gap": "Vertikale Lücke",
    "vertical_offset": "Vertikaler Offset für Schatten",
    "vertical_on_mobile": "Vertikal in mobiler Ansicht",
    "view_all_as_last_card": "„Alle anzeigen“ als letzte Karte",
    "weight": "Gewicht",
    "wrap": "Textumbruch",
    "logo_font": "Schriftart für Logo",
    "background_color": "Hintergrundfarbe",
    "size_mobile": "Mobile Größe",
    "pixel_size_mobile": "Größe in Pixel",
    "percent_size_mobile": "Größe in Prozent",
    "unit": "Einheit",
    "custom_mobile_size": "Benutzerdefinierte mobile Größe",
    "fixed_height": "Höhe in Pixeln",
    "fixed_width": "Breite in Pixeln",
    "percent_height": "Höhe in Prozent",
    "percent_width": "Breite in Prozent",
    "percent_size": "Größe in Prozent",
    "pixel_size": "Größe in Pixel",
    "hide_padding": "Padding ausblenden",
    "always_stack_buttons": "Schaltflächen immer stapeln",
    "custom_mobile_width": "Benutzerdefinierte mobile Breite",
    "shadow_opacity": "Deckkraft des Schattens",
    "show_filter_label": "Beschriftungen für angewandte Filter",
    "show_swatch_label": "Beschriftungen für Farbfelder",
    "transparent_background": "Transparenter Hintergrund",
    "read_only": "Schreibgeschützt",
    "gradient_direction": "Richtung Farbverlauf",
    "headings": "Überschriften",
    "overlay_style": "Überlagerungsstil",
    "account": "Konto",
    "align_baseline": "Text-Baseline ausrichten",
    "add_discount_code": "Rabatte im Warenkorb erlauben",
    "background_overlay": "Hintergrund-Überlagerung",
    "background_media": "Medien im Hintergrund",
    "border_thickness": "Randdicke",
    "bottom_row": "Unterste Zeile",
    "button_text_case": "Groß- oder Kleinschreibung",
    "button_text_weight": "Schriftstärke",
    "auto_open_cart_drawer": "Die Aktion „In den Warenkorb legen“ öffnet automatisch den Einschub",
    "collection_count": "Anzahl der Kollektionen",
    "custom_liquid": "Liquid-Code",
    "default": "Standard",
    "default_logo": "Standard-Logo",
    "divider_width": "Breite der Trennlinie",
    "hide_logo_on_home_page": "Logo auf der Startseite ausblenden",
    "horizontal_padding": "Horizontales Padding",
    "inverse": "Invertiert",
    "inverse_logo": "Invertiertes Logo",
    "layout_style": "Optik",
    "length": "Länge",
    "mobile_pagination": "Mobile Seitennummerierung",
    "open_row_by_default": "Reihe standardmäßig öffnen",
    "page_transition_enabled": "Seitenübergang",
    "search": "Suche",
    "search_icon": "Suchsymbol",
    "search_position": "Position",
    "search_row": "Zeile",
    "show_author": "Autor",
    "show_alignment": "Ausrichtung anzeigen",
    "show_count": "Anzahl anzeigen",
    "show_date": "Datum",
    "show_pickup_availability": "Verfügbarkeit von Abholungen anzeigen",
    "show_search": "Suche anzeigen",
    "use_inverse_logo": "Invertiertes Logo verwenden",
    "vertical_padding": "Vertikales Padding",
    "visibility": "Sichtbarkeit",
    "product_corner_radius": "Produkt-Eckradius",
    "card_corner_radius": "Karten-Eckradius",
    "alignment_mobile": "Ausrichtung Mobilgerät",
    "animation_repeat": "Animation wiederholen",
    "blurred_reflection": "Unscharfe Spiegelung",
    "card_hover_effect": "Karten-Hover-Effekt",
    "card_size": "Kartengröße",
    "collection_title_case": "Groß-/Kleinschreibung des Kollektionstitels",
    "effects": "Effekte",
    "inventory_threshold": "Schwelle für niedrigen Lagerbestand",
    "mobile_card_size": "Kartengröße für Mobilgeräte",
    "page": "Seite",
    "product_and_card_title_case": "Groß-/Kleinschreibung des Produkt- und Kartentitels",
    "product_title_case": "Groß-/Kleinschreibung des Produkttitels",
    "reflection_opacity": "Opazität der Spiegelung",
    "right_padding": "Rechtes Padding",
    "show_inventory_quantity": "Menge bei niedrigem Lagerbestand anzeigen",
    "text_label_case": "Groß-/Kleinschreibung der Beschriftung",
    "transition_to_main_product": "Übergang von Produktkarte zu Produktseite",
    "show_second_image_on_hover": "Hover-Effekt mit zweitem Bild",
    "media": "Medien",
    "product_card_carousel": "Karussell anzeigen"
  },
  "options": {
    "adapt_to_image": "An Bild anpassen",
    "apple": "Apfel",
    "arrow": "Pfeil",
    "banana": "Banane",
    "bottle": "Flasche",
    "box": "Box",
    "buttons": "Schaltflächen",
    "carrot": "Möhre",
    "center": "Zentriert",
    "chat_bubble": "Chat-Blase",
    "clipboard": "Zwischenablage",
    "contain": "Enthalten",
    "counter": "Zähler",
    "cover": "Cover",
    "custom": "Benutzerdefiniert",
    "dairy_free": "Laktosefrei",
    "dairy": "Milch",
    "dropdowns": "Dropdown-Listen",
    "dots": "Punkte",
    "dryer": "Trockner",
    "end": "Ende",
    "eye": "Auge",
    "facebook": "Facebook",
    "fire": "Feuer",
    "gluten_free": "Glutenfrei",
    "heart": "Herz",
    "horizontal": "Horizontal",
    "instagram": "Instagram",
    "iron": "Bügeleisen",
    "large": "Groß",
    "leaf": "Blatt",
    "leather": "Leder",
    "lightning_bolt": "Blitz",
    "lipstick": "Lippenstift",
    "lock": "Schloss",
    "map_pin": "Pinnnadel",
    "medium": "Mittel",
    "none": "Keiner",
    "numbers": "Zahlen",
    "nut_free": "Ohne Nüsse",
    "pants": "Hosen",
    "paw_print": "Pfotenabdruck",
    "pepper": "Pfeffer",
    "perfume": "Parfüm",
    "pinterest": "Pinterest",
    "plane": "Flugzeug",
    "plant": "Pflanze",
    "price_tag": "Preisschild",
    "question_mark": "Fragezeichen",
    "recycle": "Wiederverwenden",
    "return": "Rückgabe",
    "ruler": "Lineal",
    "serving_dish": "Servierteller",
    "shirt": "Hemd",
    "shoe": "Schuh",
    "silhouette": "Silhouette",
    "small": "Klein",
    "snapchat": "Snapchat",
    "snowflake": "Schneeflocke",
    "star": "Stern",
    "start": "Start",
    "stopwatch": "Stoppuhr",
    "tiktok": "TikTok",
    "truck": "Lieferwagen",
    "tumblr": "Tumblr",
    "twitter": "X (Twitter)",
    "vertical": "Vertikal",
    "vimeo": "Vimeo",
    "washing": "Wäsche",
    "auto": "Auto",
    "default": "Standard",
    "fill": "Füllung",
    "fit": "Passform",
    "full": "Voll",
    "full_and_page": "An den Hintergrund und die volle Seitenbreite angepasster Inhalt",
    "heading": "Titel",
    "landscape": "Querformat",
    "lg": "LG",
    "link": "Link",
    "lowercase": "Kleinschreibung",
    "m": "M",
    "outline": "Umriss",
    "page": "Seite",
    "portrait": "Hochformat",
    "s": "S",
    "sentence": "Satz",
    "solid": "Fest",
    "space_between": "Leerzeichen zwischen",
    "square": "Square",
    "uppercase": "Großbuchstaben",
    "circle": "Kreis",
    "swatches": "Farbfelder",
    "full_and_page_offset_left": "An den Hintergrund und die volle Seitenbreite angepasster Inhalt, Offset links",
    "full_and_page_offset_right": "An den Hintergrund und die volle Seitenbreite angepasster Inhalt, Offset rechts",
    "offset_left": "Offset links",
    "offset_right": "Offset rechts",
    "page_center_aligned": "Seite, mittig ausgerichtet",
    "page_left_aligned": "Seite, links ausgerichtet",
    "page_right_aligned": "Seite, rechts ausgerichtet",
    "button": "Schaltfläche",
    "caption": "Bildtext",
    "h1": "Überschrift 1",
    "h2": "Überschrift 2",
    "h3": "Überschrift 3",
    "h4": "Überschrift 4",
    "h5": "Überschrift 5",
    "h6": "Überschrift 6",
    "paragraph": "Absatz",
    "primary": "Primär",
    "secondary": "Sekundär",
    "tertiary": "Tertiär",
    "chevron_left": "Chevron nach links",
    "chevron_right": "Chevron nach rechts",
    "diamond": "Diamant",
    "grid": "Raster",
    "parallelogram": "Parallelogramm",
    "rounded": "Gerundet",
    "fit_content": "Passform",
    "pills": "Kapseln",
    "heavy": "Kräftig",
    "thin": "Dünn",
    "drawer": "Einschub",
    "preview": "Vorschau",
    "text": "Text",
    "video_uploaded": "Hochgeladen",
    "video_external_url": "Externe URL",
    "aspect_ratio": "Seitenverhältnis",
    "above_carousel": "Oberhalb des Karussells",
    "all": "Alle",
    "always": "Immer",
    "arrows_large": "Große Pfeile",
    "arrows": "Pfeile",
    "balance": "Balance",
    "bento": "Bento",
    "black": "Schwarz",
    "bluesky": "Bluesky",
    "body_large": "Text (groß)",
    "body_regular": "Text (regulär)",
    "body_small": "Text (klein)",
    "bold": "Fett",
    "bottom_left": "Unten, links",
    "bottom_right": "Unten, rechts",
    "bottom": "Unten",
    "capitalize": "In Großbuchstaben schreiben",
    "caret": "Cursor",
    "carousel": "Karussell",
    "check_box": "Kontrollkästchen",
    "chevron_large": "Große Chevrons",
    "chevron": "Chevron",
    "chevrons": "Chevrons",
    "classic": "Klassisch",
    "collection_images": "Kollektionsbilder",
    "color": "Farbe",
    "complementary": "Ergänzend",
    "dissolve": "Überblenden",
    "dotted": "Gepunktet",
    "editorial": "Editorial",
    "extra_large": "Extra groß",
    "extra_small": "Extra klein",
    "featured_collections": "Vorgestellte Kollektionen",
    "featured_products": "Vorgestellte Produkte",
    "font_primary": "Primär",
    "font_secondary": "Sekundär",
    "font_tertiary": "Tertiär",
    "forward": "Vorwärts",
    "full_screen": "Vollbild",
    "heading_extra_large": "Überschrift (extra groß)",
    "heading_extra_small": "Überschrift (extra klein)",
    "heading_large": "Überschrift (groß)",
    "heading_regular": "Überschrift (regulär)",
    "heading_small": "Überschrift (klein)",
    "icon": "Symbol",
    "image": "Bild",
    "input": "Eingabe",
    "inside_carousel": "Innenkarussell",
    "inverse_large": "Invertiert groß",
    "inverse": "Invertiert",
    "large_arrows": "Große Pfeile",
    "large_chevrons": "Große Chevrons",
    "left": "Links",
    "light": "Hell",
    "linkedin": "LinkedIn",
    "loose": "Ungebunden",
    "media_first": "Medien zuerst",
    "media_second": "Medien als zweites",
    "modal": "Modal",
    "narrow": "Schmal",
    "never": "Nie",
    "next_to_carousel": "Neben dem Karussell",
    "normal": "Normal",
    "nowrap": "Kein Zeilenumbruch",
    "off_media": "Nicht bei Medien",
    "on_media": "Bei Medien",
    "on_scroll_up": "Beim Hochscrollen",
    "one_half": "1/2",
    "one_number": "1",
    "one_third": "1/3",
    "pill": "Oval",
    "plus": "Plus",
    "pretty": "Schön",
    "price": "Preis",
    "primary_style": "Primärer Stil",
    "rectangle": "Rechteck",
    "regular": "Regulär",
    "related": "Ähnlich",
    "reverse": "Umkehren",
    "rich_text": "Rich Text",
    "right": "Rechts",
    "secondary_style": "Sekundärer Stil",
    "semibold": "Halbfett",
    "shaded": "Schattiert",
    "show_second_image": "Zweites Bild anzeigen",
    "single": "Einzeln",
    "slide_left": "Nach links schieben",
    "slide_up": "Nach oben schieben",
    "spotify": "Spotify",
    "stack": "Stapel",
    "text_only": "Nur Text",
    "threads": "Threads",
    "thumbnails": "Vorschaubilder",
    "tight": "Eng",
    "top_left": "Oben links",
    "top_right": "Oben rechts",
    "top": "Oben",
    "two_number": "2",
    "two_thirds": "2/3",
    "underline": "Unterstreichen",
    "video": "Video",
    "wide": "Breit",
    "youtube": "YouTube",
    "up": "Oben",
    "down": "Unten",
    "gradient": "Farbverlauf",
    "fixed": "Fest",
    "pixel": "Pixel",
    "percent": "Prozent",
    "accent": "Akzent",
    "below_image": "Unter Bild",
    "body": "Nachricht",
    "button_primary": "Primäre Schaltfläche",
    "button_secondary": "Sekundäre Schaltfläche",
    "compact": "Kompakt",
    "crop_to_fit": "Passend zuschneiden",
    "hidden": "Versteckt",
    "hint": "Tipp",
    "maintain_aspect_ratio": "Seitenverhältnisse beibehalten",
    "off": "Aus",
    "on_image": "Auf Bild",
    "social_bluesky": "Social: Bluesky",
    "social_facebook": "Social: Facebook",
    "social_instagram": "Social: Instagram",
    "social_linkedin": "Social: LinkedIn",
    "social_pinterest": "Social: Pinterest",
    "social_snapchat": "Social: Snapchat",
    "social_spotify": "Social: Spotify",
    "social_threads": "Social: Threads",
    "social_tiktok": "Social: TikTok",
    "social_tumblr": "Social: Tumblr",
    "social_twitter": "Social: X (Twitter)",
    "social_whatsapp": "Social: WhatsApp",
    "social_vimeo": "Social: Vimeo",
    "social_youtube": "Social: YouTube",
    "spotlight": "Spotlight",
    "standard": "Standard",
    "subheading": "Unterüberschrift",
    "blur": "Weichzeichnen",
    "lift": "Lift",
    "reveal": "Einblenden",
    "scale": "Skalierung",
    "subtle_zoom": "Zoomen"
  },
  "content": {
    "background_video": "Hintergrund-Video",
    "describe_the_video_for": "Beschreibe das Video für Kunden, die Bildschirmlesegeräte benutzen. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)",
    "width_is_automatically_optimized": "Breite wird automatisch für die mobile Nutzung optimiert.",
    "advanced": "Advanced",
    "background_image": "Hintergrundbild",
    "block_size": "Blockgröße",
    "borders": "Ränder",
    "section_size": "Abschnittsgröße",
    "slideshow_width": "Folienbreite",
    "typography": "Typografie",
    "complementary_products": "Ergänzende Produkte müssen mit der Search & Discovery-App eingerichtet werden. [Mehr Informationen](https://help.shopify.com/manual/online-store/search-and-discovery)",
    "mobile_column_optimization": "Spalten werden automatisch für die mobile Nutzung optimiert",
    "content_width": "Inhaltsbreite wird nur angewendet, wenn für die Abschnittsbreite volle Breite festgelegt ist.",
    "adjustments_affect_all_content": "Gilt für alle Inhalte in diesem Block",
    "responsive_font_sizes": "Die Größe wird automatisch auf sämtliche Bildschirmgrößen skaliert.",
    "buttons": "Schaltflächen",
    "swatches": "Farbfelder",
    "variant_settings": "Varianteneinstellungen",
    "background": "Hintergrund",
    "appearance": "Erscheinungsbild",
    "arrows": "Pfeile",
    "body_size": "Textgröße",
    "bottom_row_appearance": "Erscheinungsbild der untersten Zeile",
    "carousel_navigation": "Karussell-Navigation",
    "carousel_pagination": "Karussell-Seitennummerierung",
    "copyright": "Urheberrecht",
    "edit_logo_in_theme_settings": "Bearbeite dein Logo in den [Theme-Einstellungen](/editor?context=theme&category=logo%20and%20favicon)",
    "edit_price_in_theme_settings": "Bearbeite die Preisformatierung in den [Theme-Einstellungen](/editor?context=theme&category=currency%20code)",
    "edit_variants_in_theme_settings": "Bearbeite den Variantenstil in den [Theme-Einstellungen](/editor?context=theme&category=variants)",
    "email_signups_create_customer_profiles": "Registrierungen hinzufügen [Kundenprofile ](https://help.shopify.com/manual/customers)",
    "follow_on_shop_eligiblity": "Damit die Schaltfläche angezeigt wird, muss der Shop-Kanal installiert und Shop Pay aktiviert sein. [Mehr Informationen](https://help.shopify.com/en/manual/online-store/themes/customizing-themes/add-shop-buttons)",
    "fonts": "Schriftarten",
    "grid": "Raster",
    "heading_size": "Größe der Überschrift",
    "image": "Bild",
    "input": "Eingabe",
    "layout": "Layout",
    "link": "Link",
    "link_padding": "Link-Padding",
    "localization": "Lokalisierung",
    "logo": "Logo",
    "margin": "Rand",
    "media": "Medien",
    "media_1": "Medien 1",
    "media_2": "Medien 2",
    "menu": "Menü",
    "mobile_layout": "Mobiles Layout",
    "padding": "Padding",
    "padding_desktop": "Desktop-Padding",
    "paragraph": "Absatz",
    "policies": "Richtlinien",
    "popup": "Pop-up",
    "search": "Suche",
    "size": "Größe",
    "social_media": "Social Media",
    "submit_button": "Schaltfläche „Senden“",
    "text_presets": "Textvoreinstellungen",
    "transparent_background": "Transparenter Hintergrund",
    "typography_primary": "Primäre Typografie",
    "typography_secondary": "Sekundäre Typografie",
    "typography_tertiary": "Tertiäre Typografie",
    "mobile_size": "Mobile Größe",
    "cards_layout": "Karten-Layout",
    "section_layout": "Abschnitts-Layout",
    "mobile_width": "Breite bei mobiler Ansicht",
    "width": "Breite",
    "carousel": "Karussell",
    "colors": "Farben",
    "collection_page": "Kollektionsseite",
    "copyright_info": "Hier erfährst du, wie du [deinen Urheberrechtshinweis bearbeitest](https://help.shopify.com/manual/online-store/themes/customizing-themes/remove-powered-by-shopify-message)",
    "customer_account": "Kundenkonto",
    "edit_empty_state_collection_in_theme_settings": "Kollektion mit Status „Leer“ in [Theme-Einstellungen]](/editor?context=theme&category=search) verwalten",
    "home_page": "Startseite",
    "images": "Bilder",
    "inverse_logo_info": "Wird verwendet, wenn der transparente Header-Hintergrund auf „Invertiert“ gesetzt ist",
    "manage_customer_accounts": "[Sichtbarkeit verwalten](/admin/settings/customer_accounts) in den Kundenkontoeinstellungen. Veraltete Konten werden nicht unterstützt.",
    "manage_policies": "[Richtlinien verwalten](/admin/settings/legal)",
    "product_page": "Produktseite",
    "text": "Text",
    "thumbnails": "Vorschaubilder",
    "visibility": "Sichtbarkeit",
    "visible_if_collection_has_more_products": "Sichtbar, wenn die Kollektion mehr als die angezeigten Produkte enthält",
    "grid_layout": "Raster-Layout",
    "app_required_for_ratings": "Für Produktbewertungen wird eine App benötigt. [Mehr Informationen](https://help.shopify.com/manual/apps)",
    "icon": "Symbol",
    "manage_store_name": "[Shop-Namen verwalten ](/admin/settings/general?edit=storeName)",
    "resource_reference_collection_card": "Zeigt Kollektion aus übergeordnetem Abschnitt an",
    "resource_reference_collection_card_image": "Zeigt Bild aus übergeordneter Kollektion an",
    "resource_reference_collection_title": "Zeigt Titel aus übergeordneter Kollektion an",
    "resource_reference_product": "Verbindet sich automatisch mit übergeordnetem Produkt",
    "resource_reference_product_card": "Zeigt Produkt aus übergeordnetem Abschnitt an",
    "resource_reference_product_inventory": "Zeigt Inventar aus übergeordnetem Produkt an",
    "resource_reference_product_media": "Zeigt Medien aus dem übergeordneten Produkt an",
    "resource_reference_product_price": "Zeigt Preis aus übergeordnetem Produkt an",
    "resource_reference_product_recommendations": "Zeigt Empfehlungen auf Basis des übergeordneten Produkts an",
    "resource_reference_product_review": "Zeigt Bewertungen aus übergeordnetem Produkt an",
    "resource_reference_product_swatches": "Zeigt Farbfelder aus übergeordnetem Produkt an",
    "resource_reference_product_title": "Zeigt Titel aus übergeordnetem Produkt an",
    "resource_reference_product_variant_picker": "Zeigt Varianten aus übergeordnetem Produkt an"
  },
  "html_defaults": {
    "share_information_about_your": "<p>Teile Infos über deine Marke mit deinen Kunden. Beschreibe ein Produkt, kündige etwas an oder heiße Kunden willkommen.</p>"
  },
  "text_defaults": {
    "collapsible_row": "Einklappbare Reihe",
    "button_label": "Jetzt einkaufen",
    "heading": "Titel",
    "email_signup_button_label": "Abonnieren",
    "accordion_heading": "Akkordeon-Überschrift",
    "contact_form_button_label": "Senden",
    "popup_link": "Pop-up-Link",
    "sign_up": "Registrieren",
    "welcome_to_our_store": "Willkommen in unserem Shop",
    "be_bold": "Sei mutig.",
    "shop_our_latest_arrivals": "Durchstöbere unsere aktuellsten Neuheiten!"
  },
  "info": {
    "video_alt_text": "Beschreibe das Video für Nutzer von unterstützender Technologie.",
    "video_autoplay": "Videos werden standardmäßig stummgeschaltet",
    "video_external": "YouTube- oder Vimeo-URL verwenden",
    "carousel_layout_on_mobile": "Karussell wird auf Mobilgerät verwendet",
    "carousel_hover_behavior_not_supported": "„Karussell“-Hover wird nicht unterstützt, wenn der Typ „Karussell“ auf der Abschnittsebene ausgewählt ist",
    "link_info": "Optional: macht das Symbol anklickbar",
    "checkout_buttons": "Ermöglicht es Käufern, schneller auszuchecken, und kann die Conversion optimieren. [Mehr Informationen](https://help.shopify.com/manual/online-store/dynamic-checkout)",
    "custom_heading": "Benutzerdefinierte Überschrift",
    "edit_presets_in_theme_settings": "Bearbeite Voreinstellungen in den [Theme-Einstellungen](/editor?context=theme&category=typography)",
    "enable_filtering_info": "Passe Filter an mit der [Search & Discovery-App](https://help.shopify.com/manual/online-store/search-and-discovery/filters)",
    "grid_layout_on_mobile": "Das Raster-Layout wird für die mobile Ansicht verwendet",
    "manage_countries_regions": "[Manage countries/regions](/admin/settings/markets)",
    "manage_languages": "[Manage languages](/admin/settings/languages)",
    "transparent_background": "Überprüfe alle Vorlagen, in denen ein transparenter Hintergrund verwendet wird, auf ihre Lesbarkeit",
    "logo_font": "Gilt nur, wenn kein Logo ausgewählt wurde",
    "aspect_ratio_adjusted": "In einigen Layouts angepasst",
    "auto_open_cart_drawer": "Wenn er aktiviert ist, wird der Warenkorbeinschub automatisch geöffnet, wenn ein Produkt zum Warenkorb hinzugefügt wird.",
    "custom_liquid": "Füge App-Snippets oder anderen Code hinzu, um fortgeschrittene Anpassungen zu erstellen. [Mehr Informationen](https://shopify.dev/docs/api/liquid)",
    "applies_on_image_only": "Gilt nur für Bilder",
    "hover_effects": "Bezieht sich auf Produkt- und Kollektionskarten",
    "pills_usage": "Für angewandte Filter, Rabattcodes und Suchvorschläge"
  },
  "categories": {
    "product_list": "Produktliste",
    "basic": "Basic",
    "collection": "Kollektion",
    "collection_list": "Kollektionsliste",
    "footer": "Fußzeile",
    "forms": "Formulare",
    "header": "Header",
    "layout": "Layout",
    "links": "Links",
    "product": "Produkt",
    "banners": "Banner",
    "collections": "Kollektionen",
    "custom": "Benutzerdefiniert",
    "decorative": "Dekorativ",
    "products": "Produkte",
    "other_sections": "Weitere",
    "storytelling": "Storytelling"
  }
}
