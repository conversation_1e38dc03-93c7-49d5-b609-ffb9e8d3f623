<!doctype html>
<html
  class="no-js{% if request.design_mode %} shopify-design-mode{% endif %}"
  lang="{{ request.locale.iso_code }}"
>
  <head>
    {%- render 'stylesheets' -%}

    {%- if settings.favicon != blank -%}
      <link
        rel="icon"
        type="image/png"
        href="{{ settings.favicon | image_url: width: 32, height: 32 }}"
      >
    {%- endif -%}

    {% comment %} This a way to wait for main content to load when navigating to a new page so that the view transitions can work consistently {% endcomment %}
    <link
      rel="expect"
      href="#MainContent"
      blocking="render"
      id="view-transition-render-blocker"
    >

    {%- render 'meta-tags' -%}
    {%- render 'fonts' -%}
    {%- render 'scripts' -%}
    {%- render 'theme-styles-variables' -%}
    {%- render 'color-schemes' -%}

    {% if request.design_mode %}
      {%- render 'theme-editor' -%}
    {% endif %}

    {{ content_for_header }}
  </head>

  <body class="page-width-{{ settings.page_width }}">
    <main
      id="MainContent"
      class="content-for-layout"
      role="main"
    >
      {{ content_for_layout }}
    </main>

    <div class="password-enter">
      <p class="password-enter__powered-by">
        {{ 'content.powered_by' | t }}
        <a
          href="//shopify.com"
          rel="nofollow"
          target="_blank"
          aria-label="Shopify"
          aria-describedby="a11y-new-window-message"
        >
          {{- 'icon-shopify.svg' | inline_asset_content -}}
        </a>
      </p>
      <div class="password-enter__links">
        <button
          class="button-unstyled password-enter__button"
          on:click="#password-form/showDialog"
        >
          {{ 'actions.enter_using_password' | t }}
        </button>
        <p class="password-enter__admin-link">
          {{ 'content.store_owner_link_html' | t: link: '/admin' }}
        </p>
      </div>
      <ul hidden>
        <li id="a11y-new-window-message">{{ 'accessibility.new_window' | t }}</li>
      </ul>
    </div>

    {%- form 'storefront_password' -%}
      <dialog-component id="password-form">
        <dialog
          ref="dialog"
          class="password-dialog"
          {% if form.errors %}
            open
          {% endif %}
        >
          <div class="password-dialog__header">
            <button
              class="button-unstyled password-dialog__close-button"
              on:click="dialog-component/closeDialog"
              type="button"
            >
              <span
                class="svg-wrapper svg-wrapper--small"
                title="{{ 'actions.close' | t }}"
              >
                {{ 'icon-close.svg' | inline_asset_content }}
              </span>
            </button>
          </div>
          <div class="password-dialog__content">
            <h3 class="password-dialog__title">
              {{ 'actions.enter_password' | t }}
            </h3>

            <div class="email-signup__input-group">
              <label
                for="Password"
                class="visually-hidden"
              >
                {{ 'actions.enter_password' | t }}
              </label>

              <input
                type="password"
                name="password"
                id="Password"
                class="field__input field__input--button-radius field__input--button-padding"
                autocomplete="current-password"
                {% if form.errors %}
                  aria-invalid="true"
                  aria-describedby="PasswordLoginForm-password-error"
                {%- endif -%}
                placeholder="{{ 'placeholders.password' | t }}"
                autofocus
              >

              <button
                class="button password-dialog__submit-button"
                type="submit"
              >
                {{ 'actions.submit' | t }}
              </button>
            </div>

            {%- if form.errors -%}
              <div
                id="PasswordLoginForm-password-error"
                class="email-signup__message email-signup__message--error"
                tabindex="-1"
              >
                <svg
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                  focusable="false"
                  aria-hidden="true"
                  class="icon-error"
                >
                  {%- render 'icon', icon: 'error'  -%}
                </svg>
                {{ 'content.wrong_password' | t }}
              </div>
            {%- endif -%}
          </div>
        </dialog>
      </dialog-component>
    {%- endform -%}

    {% # theme-check-disable ParserBlockingScript %}
    <script src="{{ 'critical.js' | asset_url }}"></script>
    {% # theme-check-enable ParserBlockingScript %}
  </body>
</html>
