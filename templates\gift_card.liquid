{% layout none %}

<!doctype html>
<html lang="{{ request.locale.iso_code }}">
  <head>
    <script type="importmap">
      {
        "imports": {
          "component": "{{ 'component.js' | asset_url }}",
          "utilities": "{{ 'utilities.js' | asset_url }}",
          "qr-code-generator": "{{ 'qr-code-generator.js' | asset_url }}"
        }
      }
    </script>
    {% # theme-check-disable ParserBlockingScript %}
    <script src="{{ 'critical.js' | asset_url }}"></script>
    {% # theme-check-enable ParserBlockingScript %}
    <script
      src="{{ 'qr-code-image.js' | asset_url }}"
      type="module"
    ></script>
    <script
      src="{{ 'copy-to-clipboard.js' | asset_url }}"
      type="module"
    ></script>
    <meta charset="utf-8">
    <meta
      http-equiv="X-UA-Compatible"
      content="IE=edge"
    >
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1"
    >
    <meta
      name="theme-color"
      content="{{ settings.color_schemes.scheme-1.settings.background }}"
    >
    <link
      rel="canonical"
      href="{{ canonical_url }}"
    >

    {%- if settings.favicon != blank -%}
      <link
        rel="icon"
        type="image/png"
        href="{{ settings.favicon | image_url: width: 32, height: 32 }}"
      >
    {%- endif -%}

    {%- unless settings.type_primary_font.system?
      and settings.type_secondary_font.system?
      and settings.type_tertiary_font.system?
    -%}
      <link
        rel="preconnect"
        href="https://fonts.shopifycdn.com"
        crossorigin
      >
    {%- endunless -%}

    {%- assign formatted_balance = gift_card.balance | money_without_trailing_zeros | strip_html -%}

    <title>{{ 'gift_cards.issued.title' | t: value: formatted_balance, shop: shop.name }}</title>

    <meta
      name="description"
      content="{{ 'gift_cards.issued.subtext' | t }}"
    >

    {{ content_for_header }}

    {% comment %}theme-check-disable AssetPreload{% endcomment %}
    {%- unless settings.type_primary_font.system? -%}
      <link
        rel="preload"
        as="font"
        href="{{ settings.type_primary_font | font_url }}"
        type="font/woff2"
        crossorigin
      >
    {%- endunless -%}
    {%- unless settings.type_secondary_font.system? -%}
      <link
        rel="preload"
        as="font"
        href="{{ settings.type_secondary_font | font_url }}"
        type="font/woff2"
        crossorigin
      >
    {%- endunless -%}
    {%- unless settings.type_tertiary_font.system? -%}
      <link
        rel="preload"
        as="font"
        href="{{ settings.type_tertiary_font | font_url }}"
        type="font/woff2"
        crossorigin
      >
    {%- endunless -%}

    {% comment %}theme-check-enable AssetPreload{% endcomment %}
    {%- render 'theme-styles-variables' -%}
    {%- render 'color-schemes' -%}

    {{ 'base.css' | asset_url | stylesheet_tag }}
    {{ 'template-giftcard.css' | asset_url | stylesheet_tag }}
  </head>

  <body class="gift-card">
    <header>
      <div class="gift-card__text-wrapper">
        <h2>{{ shop.name }}</h2>
      </div>
    </header>

    <main class="gift-card__main">
      <div class="gift-card__image-wrapper">
        {%- if settings.logo != blank -%}
          {%- assign logo_alt = settings.logo.alt | default: shop.name | escape -%}
          {{
            settings.logo
            | image_url: width: 324
            | image_tag: class: 'gift-card__image gift-card__image-border', alt: logo_alt
          }}
        {%- else %}
          <img
            class="gift-card__image"
            src="{{ 'gift-card/card.svg' | shopify_asset_url }}"
            alt=""
            height="200"
            width="334"
            loading="eager"
          >
        {%- endif %}
      </div>
      <div class="gift-card__price">
        <h2>
          {{ gift_card.balance | money_with_currency }}
        </h2>
        {%- if gift_card.enabled == false or gift_card.expired -%}
          <p class="gift-card__badge gift-card__badge--expired">{{ 'gift_cards.issued.expired' | t }}</p>
        {%- endif -%}
      </div>
      {% if gift_card.expires_on %}
        {%- assign gift_card_expiration_date = gift_card.expires_on | date: '%B %e, %Y' -%}
        <h4 class="gift-card__text-wrapper">
          {{ 'gift_cards.issued.expiration_date' | t: expires_on: gift_card_expiration_date }}
        </h4>
      {% endif %}
      <div class="gift-card__text-wrapper">
        <h4>{{ 'gift_cards.issued.how_to_use_gift_card' | t }}</h4>
        <h3
          class="gift-card__code"
        >
          {{ gift_card.code | format_code }}
        </h3>
      </div>
      <qr-code-image
        class="gift-card__qr-code"
        data-identifier="{{ gift_card.qr_identifier }}"
        width="72"
        height="72"
        alt="{{ 'gift_cards.issued.qr_image_alt' | t }}"
      ></qr-code-image>
      <div class="gift-card__buttons buttons-container">
        {%- if gift_card.pass_url -%}
          <a
            href="{{ gift_card.pass_url }}"
            class="gift-card__buttons-full-width"
          >
            <img
              src="{{ 'gift-card/add-to-apple-wallet.svg' | shopify_asset_url }}"
              width="120"
              height="40"
              alt="{{ 'gift_cards.issued.add_to_apple_wallet' | t }}"
              loading="lazy"
            >
          </a>
        {%- endif -%}
        <copy-to-clipboard-component
          class="gift-card__buttons-full-width gift-card__copy-button"
          text-to-copy="{{ gift_card.code }}"
        >
          <span
            class="form__message visually-hidden"
            ref="copySuccessMessage"
            role="status"
          >
            <span class="svg-wrapper icon-success">
              {{ 'icon-checkmark.svg' | inline_asset_content }}
            </span>
            {{ 'gift_cards.issued.copy_code_success' | t }}
          </span>
          <button
            class="button gift-card__buttons-full-width"
            on:click="/copyToClipboard"
          >
            {{ 'gift_cards.issued.copy_code' | t }}
          </button>
        </copy-to-clipboard-component>
        <a
          href="{{ shop.url }}"
          class="gift-card__buttons-full-width button button-secondary"
          target="_blank"
          rel="noopener"
          aria-describedby="a11y-new-window-message"
        >
          {{ 'gift_cards.issued.shop_link' | t }}
        </a>
      </div>
    </main>

    <div hidden>
      <span id="a11y-new-window-message">{{ 'accessibility.new_window' | t }}</span>
    </div>
  </body>
</html>
