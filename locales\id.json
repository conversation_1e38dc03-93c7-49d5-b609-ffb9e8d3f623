/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin language editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "blocks": {
    "load_video": "Muat video: {{ description }}",
    "sold_out": "Habis",
    "email_signup": {
      "label": "Email",
      "placeholder": "Alamat email",
      "success": "Terima kasih sudah berlangganan!"
    },
    "filter": "Filter",
    "payment_methods": "Metode pembayaran",
    "contact_form": {
      "name": "<PERSON><PERSON>",
      "email": "<PERSON><PERSON>",
      "phone": "Telepon",
      "comment": "Komentar",
      "post_success": "Terima kasih sudah menghubungi kami. Kami akan segera menghubungi Anda.",
      "error_heading": "Mohon sesuaikan:"
    }
  },
  "accessibility": {
    "play_model": "Putar model 3D",
    "play_video": "Putar video",
    "unit_price": "Harga satuan",
    "country_results_count": "{{ count }} hasil",
    "slideshow_pause": "Jeda slideshow",
    "slideshow_play": "Putar slideshow",
    "remove_item": "Hapus {{ title}}",
    "skip_to_text": "Langsung ke konten",
    "skip_to_product_info": "Langsung ke informasi produk",
    "skip_to_results_list": "Langsung ke daftar hasil",
    "new_window": "Membuka di jendela baru.",
    "close_dialog": "Tutup dialog",
    "reset_search": "Reset pencarian",
    "search_results_count": "{{ count }} hasil pencarian ditemukan untuk \"{{ query }}\"",
    "search_results_no_results": "Tidak ditemukan hasil untuk \"{{ query }}\"",
    "slideshow_next": "Slide berikutnya",
    "slideshow_previous": "Slide sebelumnya",
    "filters": "Filter",
    "account": "Buka menu akun",
    "cart": "Keranjang",
    "cart_count": "Total item di keranjang",
    "filter_count": {
      "one": "{{ count }} filter diterapkan",
      "other": "{{ count }} filter diterapkan"
    },
    "menu": "Menu",
    "country_region": "Negara/Wilayah",
    "slide_status": "Slide {{ index }} dari {{ length }}",
    "scroll_to": "Gulir ke {{ title }}",
    "loading_product_recommendations": "Memuat rekomendasi produk",
    "discount": "Pakai kode diskon",
    "discount_applied": "Kode diskon yang dipakai: {{ code }}",
    "open_cart_drawer": "Buka troli",
    "inventory_status": "Status Inventaris",
    "pause_video": "Jeda video",
    "find_country": "Temukan negara",
    "localization_region_and_language": "Buka selektor wilayah dan bahasa",
    "open_search_modal": "Buka pencarian",
    "decrease_quantity": "Kurangi jumlah",
    "increase_quantity": "Tambah jumlah",
    "quantity": "Jumlah",
    "rating": "Peringkat produk ini adalah {{ rating }} dari 5"
  },
  "actions": {
    "add_to_cart": "Tambahkan ke keranjang",
    "clear_all": "Hapus semua",
    "remove": "Hapus",
    "view_in_your_space": "Lihat di lokasi Anda",
    "show_filters": "Filter",
    "clear": "Kosongkan",
    "continue_shopping": "Lanjutkan belanja",
    "log_in_html": "Sudah punya akun? <a href=\"{{ link }}\">Login</a> untuk checkout lebih cepat.",
    "see_items": {
      "one": "Lihat {{ count }} item",
      "other": "Lihat {{ count }} item"
    },
    "view_all": "Lihat semua",
    "add": "Tambah",
    "choose": "Pilih",
    "added": "Ditambahkan",
    "show_less": "Sembunyikan lainnya",
    "show_more": "Selengkapnya",
    "close": "Tutup",
    "more": "Selengkapnya",
    "zoom": "Perbesar",
    "close_dialog": "Tutup dialog",
    "reset": "Reset",
    "remove_discount": "Hapus diskon {{ code }}",
    "enter_using_password": "Masuk dengan sandi",
    "submit": "Kirim",
    "enter_password": "Masukkan sandi",
    "view_store_information": "Lihat informasi toko",
    "back": "Kembali",
    "log_in": "Masuk",
    "log_out": "Logout",
    "apply": "Pakai",
    "sign_in_options": "Opsi masuk lainnya",
    "open_image_in_full_screen": "Buka gambar dalam layar penuh",
    "sign_up": "Daftar",
    "sort": "Urutkan",
    "show_all_options": "Tampilkan semua opsi"
  },
  "content": {
    "reviews": "ulasan",
    "language": "Bahasa",
    "localization_region_and_language": "Wilayah dan bahasa",
    "no_results_found": "Hasil tidak ditemukan",
    "cart_total": "Total keranjang",
    "your_cart_is_empty": "Keranjang Anda kosong",
    "product_image": "Gambar produk",
    "product_information": "Informasi produk",
    "quantity": "Jumlah",
    "product_total": "Total produk",
    "cart_estimated_total": "Estimasi total",
    "seller_note": "Instruksi khusus",
    "cart_subtotal": "Subtotal",
    "discounts": "Diskon",
    "discount": "Diskon",
    "duties_and_taxes_included_shipping_at_checkout_with_policy_html": "Termasuk bea cukai dan pajak. Diskon dan <a href=\"{{ link }}\">biaya pengiriman</a> dihitung saat checkout.",
    "duties_and_taxes_included_shipping_at_checkout_without_policy": "Termasuk bea cukai dan pajak. Diskon dan biaya pengiriman dihitung saat checkout.",
    "taxes_included_shipping_at_checkout_with_policy_html": "Termasuk pajak. Diskon dan <a href=\"{{ link }}\">biaya pengiriman</a> dihitung saat checkout.",
    "taxes_included_shipping_at_checkout_without_policy": "Termasuk pajak. Diskon dan biaya pengiriman dihitung saat checkout.",
    "duties_included_taxes_at_checkout_shipping_at_checkout_with_policy_html": "Termasuk bea cukai. Pajak, diskon, dan <a href=\"{{ link }}\">biaya pengiriman</a> dihitung saat checkout.",
    "duties_included_taxes_at_checkout_shipping_at_checkout_without_policy": "Termasuk bea cukai. Pajak, diskon, dan biaya pengiriman dihitung saat checkout.",
    "taxes_at_checkout_shipping_at_checkout_with_policy_html": "Pajak, diskon, dan <a href=\"{{ link }}\">biaya pengiriman</a> dihitung saat checkout.",
    "taxes_at_checkout_shipping_at_checkout_without_policy": "Pajak, diskon, dan biaya pengiriman dihitung saat checkout.",
    "checkout": "Check out",
    "cart_title": "Keranjang",
    "price": "Harga",
    "price_regular": "Harga reguler",
    "price_compare_at": "Bandingkan dengan harga",
    "price_sale": "Harga obral",
    "duties_and_taxes_included": "Termasuk bea cukai dan pajak.",
    "duties_included": "Termasuk bea cukai.",
    "shipping_policy_html": "<a href=\"{{ link }}\">Biaya pengiriman</a> dihitung saat checkout.",
    "taxes_included": "Termasuk pajak.",
    "product_badge_sold_out": "Habis",
    "product_badge_sale": "Promo",
    "search_input_label": "Cari",
    "search_input_placeholder": "Cari",
    "search_results": "Hasil pencarian",
    "search_results_label": "Hasil pencarian",
    "search_results_no_results": "Tidak ada hasil yang ditemukan untuk \"{{ terms }}\". Coba pencarian lain.",
    "search_results_resource_articles": "Postingan blog",
    "search_results_resource_collections": "Koleksi",
    "search_results_resource_pages": "Halaman",
    "search_results_resource_products": "Produk",
    "search_results_resource_queries": "Saran pencarian",
    "search_results_view_all": "Lihat semua",
    "search_results_view_all_button": "Lihat semua",
    "search_results_resource_products_count": {
      "one": "{{ count }} produk",
      "other": "{{ count }} produk"
    },
    "grid_view": {
      "default_view": "Default",
      "grid_fieldset": "Grid kolom",
      "single_item": "Tunggal",
      "zoom_out": "Perkecil"
    },
    "recently_viewed_products": "Baru saja dilihat",
    "collection_placeholder": "Judul koleksi",
    "product_card_placeholder": "Judul produk",
    "unavailable": "Tidak tersedia",
    "product_count": "Jumlah produk",
    "item_count": {
      "one": "{{ count }} item",
      "other": "{{ count }} item"
    },
    "errors": "Kesalahan",
    "price_from": "Mulai {{ price }}",
    "search": "Cari",
    "search_results_no_results_check_spelling": "Tidak ada hasil yang ditemukan untuk \"{{ terms }}\". Periksa ejaan atau gunakan kata atau frasa yang berbeda.",
    "featured_products": "Produk unggulan",
    "no_products_found": "Tidak ada produk yang ditemukan.",
    "use_fewer_filters_html": "Coba kurangi filter, atau <a class=\"{{ class }}\" href=\"{{ link }}\">hapus semua filter</a>.",
    "filters": "Filter",
    "price_filter_html": "Harga tertingginya adalah {{ price }}",
    "blog_details_separator": "|",
    "read_more": "Baca selengkapnya...",
    "discount_code": "Kode diskon",
    "pickup_available_at_html": "Pengambilan dapat dilakukan di <b>{{ location }}</b>",
    "pickup_available_in": "Pengambilan dapat dilakukan pada {{ pickup_time }}",
    "pickup_not_available": "Pengambilan saat ini tidak tersedia",
    "pickup_ready_in": "{{ pickup_time }}",
    "wrong_password": "Sandi salah",
    "account_title": "Akun",
    "account_title_personalized": "Hai {{ first_name }}",
    "account_orders": "Pesanan",
    "account_profile": "Profil",
    "duties_and_taxes_included_shipping_at_checkout_with_policy_without_discounts_html": "Termasuk bea cukai dan pajak. Biaya pengiriman dihitung saat checkout.",
    "duties_and_taxes_included_shipping_at_checkout_without_policy_without_discounts": "Termasuk bea cukai dan pajak. Biaya pengiriman dihitung saat checkout.",
    "duties_included_taxes_at_checkout_shipping_at_checkout_with_policy_without_discounts_html": "Termasuk bea cukai. Biaya pengiriman dihitung saat checkout.",
    "duties_included_taxes_at_checkout_shipping_at_checkout_without_policy_without_discounts": "Termasuk bea cukai. Biaya pengiriman dihitung saat checkout.",
    "taxes_at_checkout_shipping_at_checkout_with_policy_without_discounts_html": "Pajak dan <a href=\"{{ link }}\">biaya pengiriman</a> dihitung saat checkout.",
    "taxes_at_checkout_shipping_at_checkout_without_policy_without_discounts": "Pajak dan biaya pengiriman dihitung saat checkout.",
    "taxes_included_shipping_at_checkout_with_policy_without_discounts_html": "Termasuk pajak. Biaya pengiriman dihitung saat checkout.",
    "taxes_included_shipping_at_checkout_without_policy_without_discounts": "Termasuk pajak. Biaya pengiriman dihitung saat checkout.",
    "view_more_details": "Lihat detail lebih lanjut",
    "inventory_low_stock": "Stok sedikit",
    "inventory_in_stock": "Tersedia",
    "inventory_out_of_stock": "Stok habis",
    "page_placeholder_title": "Judul halaman",
    "page_placeholder_content": "Pilih halaman untuk menampilkan kontennya.",
    "placeholder_image": "Gambar placeholder",
    "inventory_low_stock_show_count": {
      "one": "Tersisa {{ count }}",
      "other": "Tersisa {{ count }}"
    },
    "discount_code_error": "Kode diskon tidak dapat diterapkan ke keranjang Anda",
    "shipping_policy": "Biaya pengiriman dihitung saat checkout.",
    "shipping_discount_error": "Diskon biaya pengiriman akan ditampilkan saat checkout setelah menambahkan alamat",
    "powered_by": "Toko ini didukung oleh",
    "store_owner_link_html": "Anda pemilik toko? <a href=\"{{ link }}\">Login di sini</a>"
  },
  "gift_cards": {
    "issued": {
      "how_to_use_gift_card": "Gunakan kode voucher secara online atau kode QR di toko",
      "title": "Ini dia voucher senilai {{ value }} Anda untuk {{ shop }}!",
      "subtext": "Voucher Anda",
      "shop_link": "Kunjungi toko online",
      "add_to_apple_wallet": "Tambahkan ke Apple Wallet",
      "qr_image_alt": "Kode QR — pindai untuk menukarkan voucher",
      "copy_code": "Salin kode voucher",
      "expiration_date": "Kedaluwarsa pada {{ expires_on }}",
      "copy_code_success": "Kode berhasil disalin",
      "expired": "Kedaluwarsa"
    }
  },
  "placeholders": {
    "password": "Sandi",
    "search": "Cari",
    "product_title": "Judul produk",
    "collection_title": "Judul koleksi"
  },
  "products": {
    "product": {
      "add_to_cart": "Tambahkan ke keranjang",
      "added_to_cart": "Ditambahkan ke keranjang",
      "adding_to_cart": "Menambahkan...",
      "add_to_cart_error": "Kesalahan saat menambahkan ke keranjang",
      "sold_out": "Habis",
      "unavailable": "Tidak Tersedia"
    }
  },
  "fields": {
    "separator": "hingga"
  },
  "blogs": {
    "article": {
      "comment_author_separator": "•",
      "comments_heading": {
        "one": "{{ count }} komentar",
        "other": "{{ count }} komentar"
      }
    },
    "comment_form": {
      "email": "Email",
      "error": "Komentar gagal dikirim, perbaiki hal-hal berikut:",
      "heading": "Tulis komentar",
      "message": "Pesan",
      "moderated": "Ingat, komentar perlu disetujui sebelum dipublikasikan.",
      "name": "Nama",
      "post": "Posting komentar",
      "success_moderated": "Komentar diposting, menunggu moderasi",
      "success": "Komentar diposting"
    }
  }
}
