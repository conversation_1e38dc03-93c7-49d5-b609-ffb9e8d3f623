{% # import schema from '../schemas/blocks/payment-icons.js' %}

<div
  class="
    payment-icons
    spacing-style
  "
  style="
    {% render 'spacing-padding', settings: block.settings %}
    --icon-gap: {{ block.settings.gap }}px;
    --alignment: {{ block.settings.horizontal_alignment }};
  "
  {{ block.shopify_attributes }}
>
  <span class="visually-hidden">{{ 'blocks.payment_methods' | t }}</span>
  <ul
    class="payment-icons__list"
    role="list"
  >
    {%- for type in shop.enabled_payment_types -%}
      <li class="payment-icons__item">
        {{ type | payment_type_svg_tag: class: 'icon icon--full-color' }}
      </li>
    {%- endfor -%}
  </ul>
</div>

{% stylesheet %}
  .payment-icons {
    width: 100%;
  }

  .payment-icons__list {
    display: flex;
    align-items: center;
    justify-content: var(--alignment);
    flex-wrap: wrap;
    gap: var(--icon-gap);
    margin: 0;
    padding: 0;
  }

  .payment-icons__item {
    display: flex;
    align-items: center;
  }
{% endstylesheet %}

{% schema %}
{
  "name": "t:names.payment_icons",
  "tag": null,
  "settings": [
    {
      "type": "select",
      "id": "horizontal_alignment",
      "label": "t:settings.alignment",
      "options": [
        {
          "value": "flex-start",
          "label": "t:options.left"
        },
        {
          "value": "center",
          "label": "t:options.center"
        },
        {
          "value": "flex-end",
          "label": "t:options.right"
        },
        {
          "value": "space-between",
          "label": "t:options.space_between"
        }
      ],
      "default": "flex-start"
    },
    {
      "type": "range",
      "id": "gap",
      "label": "t:settings.gap",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 10
    },
    {
      "type": "header",
      "content": "t:content.padding"
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "t:settings.top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "t:settings.bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-inline-start",
      "label": "t:settings.left",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-inline-end",
      "label": "t:settings.right",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "t:names.payment_icons",
      "category": "t:categories.footer"
    }
  ]
}
{% endschema %}
