/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "search": {
      "type": "search-header",
      "blocks": {
        "heading": {
          "type": "_heading",
          "static": true,
          "settings": {
            "type_preset": "h2",
            "font": "var(--font-primary--family)",
            "font_size": "",
            "line_height": "normal",
            "letter_spacing": "normal",
            "case": "none",
            "wrap": "pretty",
            "color": "",
            "text": "",
            "read_only": true,
            "alignment": "left",
            "show_alignment": true,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {}
        },
        "search": {
          "type": "_search-input",
          "static": true,
          "settings": {
            "width": "custom",
            "custom_width": 50,
            "inherit_color_scheme": true,
            "color_scheme": ""
          },
          "blocks": {}
        }
      },
      "settings": {
        "alignment": "flex-start",
        "color_scheme": "scheme-1",
        "padding-block-start": 40,
        "padding-block-end": 0
      }
    },
    "main": {
      "type": "search-results",
      "blocks": {
        "filters": {
          "type": "filters",
          "static": true,
          "settings": {
            "enable_filtering": true,
            "filter_style": "horizontal",
            "filter_width": "centered",
            "text_label_case": "default",
            "show_swatch_label": false,
            "show_filter_label": false,
            "enable_sorting": true,
            "enable_grid_density": true,
            "inherit_color_scheme": true,
            "color_scheme": "",
            "padding-block-start": 8,
            "padding-block-end": 8,
            "padding-inline-start": 0,
            "padding-inline-end": 0,
            "facets_margin_bottom": 8,
            "facets_margin_right": 20
          },
          "blocks": {}
        },
        "product-card": {
          "type": "_product-card",
          "static": true,
          "settings": {
            "product_card_gap": 8,
            "inherit_color_scheme": true,
            "color_scheme": "",
            "border": "none",
            "border_width": 1,
            "border_opacity": 100,
            "border_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {
            "card-gallery": {
              "type": "_product-card-gallery",
              "settings": {
                "image_ratio": "portrait",
                "border": "none",
                "border_width": 1,
                "border_opacity": 100,
                "border_radius": 0,
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              },
              "blocks": {}
            },
            "group_r3FQB7": {
              "type": "_product-card-group",
              "name": "t:names.group",
              "settings": {
                "link": "",
                "open_in_new_tab": false,
                "content_direction": "column",
                "vertical_on_mobile": true,
                "horizontal_alignment": "flex-start",
                "vertical_alignment": "center",
                "align_baseline": false,
                "horizontal_alignment_flex_direction_column": "flex-start",
                "vertical_alignment_flex_direction_column": "center",
                "gap": 4,
                "width": "fill",
                "custom_width": 100,
                "width_mobile": "fill",
                "custom_width_mobile": 100,
                "height": "fit",
                "custom_height": 100,
                "inherit_color_scheme": true,
                "color_scheme": "",
                "background_media": "none",
                "video_position": "cover",
                "background_image_position": "cover",
                "border": "none",
                "border_width": 1,
                "border_opacity": 100,
                "border_radius": 0,
                "toggle_overlay": false,
                "overlay_color": "#00000026",
                "overlay_style": "solid",
                "gradient_direction": "to top",
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              },
              "blocks": {
                "product_title_ECggnD": {
                  "type": "product-title",
                  "name": "t:names.product_title",
                  "settings": {
                    "width": "fit-content",
                    "max_width": "normal",
                    "alignment": "left",
                    "type_preset": "rte",
                    "font": "var(--font-body--family)",
                    "font_size": "1rem",
                    "line_height": "normal",
                    "letter_spacing": "normal",
                    "case": "none",
                    "wrap": "pretty",
                    "color": "var(--color-foreground)",
                    "background": false,
                    "background_color": "#00000026",
                    "corner_radius": 0,
                    "padding-block-start": 0,
                    "padding-block-end": 0,
                    "padding-inline-start": 0,
                    "padding-inline-end": 0
                  },
                  "blocks": {}
                },
                "price_CTHraa": {
                  "type": "price",
                  "name": "t:names.product_price",
                  "settings": {
                    "show_sale_price_first": true,
                    "show_installments": false,
                    "show_tax_info": false,
                    "type_preset": "h6",
                    "width": "100%",
                    "alignment": "left",
                    "font": "var(--font-body--family)",
                    "font_size": "1rem",
                    "line_height": "normal",
                    "letter_spacing": "normal",
                    "case": "none",
                    "color": "var(--color-foreground)",
                    "padding-block-start": 0,
                    "padding-block-end": 0,
                    "padding-inline-start": 0,
                    "padding-inline-end": 0
                  },
                  "blocks": {}
                },
                "swatches_hndnaD": {
                  "type": "swatches",
                  "name": "t:names.swatches",
                  "settings": {
                    "product_swatches_alignment": "flex-start",
                    "product_swatches_alignment_mobile": "flex-start",
                    "hide_padding": false,
                    "product_swatches_padding_top": 4,
                    "product_swatches_padding_bottom": 0,
                    "product_swatches_padding_left": 0,
                    "product_swatches_padding_right": 0
                  },
                  "blocks": {}
                }
              },
              "block_order": [
                "product_title_ECggnD",
                "price_CTHraa",
                "swatches_hndnaD"
              ]
            }
          },
          "block_order": [
            "card-gallery",
            "group_r3FQB7"
          ]
        }
      },
      "settings": {
        "layout_type": "grid",
        "product_card_size": "medium",
        "mobile_product_card_size": "small",
        "product_grid_width": "centered",
        "full_width_on_mobile": true,
        "columns_gap_horizontal": 16,
        "columns_gap_vertical": 16,
        "padding-inline-start": 0,
        "padding-inline-end": 0,
        "color_scheme": "scheme-1",
        "padding-block-start": 40,
        "padding-block-end": 40
      }
    }
  },
  "order": [
    "search",
    "main"
  ]
}
