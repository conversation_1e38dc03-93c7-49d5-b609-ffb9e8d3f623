{% # import schema from '../schemas/blocks/buy-buttons.js' %}
{% liquid
  assign product = closest.product
  if request.visual_preview_mode and product == blank
    assign product = collections.all.products.first
  endif

  assign variant = closest.product.selected_or_first_available_variant
  assign inventory_quantity = variant.inventory_quantity
  assign inventory_policy = variant.inventory_policy

  if variant.inventory_management == 'shopify'
    assign inventory_managed = true
  endif

  if variant.quantity_rule.min > variant.inventory_quantity and inventory_managed and inventory_policy == 'deny'
    assign quantity_rule_soldout = true
  endif

  if inventory_managed
    if inventory_quantity <= 0 and inventory_policy == 'deny' or quantity_rule_soldout
      assign can_add_to_cart = false
      assign add_to_cart_text = 'products.product.sold_out' | t
    else
      assign can_add_to_cart = true
      assign add_to_cart_text = 'products.product.add_to_cart' | t
    endif
  else
    if closest.product.selected_or_first_available_variant != null
      assign can_add_to_cart = true
      assign add_to_cart_text = 'products.product.add_to_cart' | t
    else
      assign can_add_to_cart = false
      assign add_to_cart_text = 'products.product.unavailable' | t
    endif
  endif
%}

<span
  class="buy-buttons-block buy-buttons-block--{{ block.id }}"
  {{ block.shopify_attributes }}
>
  {%- if product != blank -%}
    {%- assign product_form_id = 'BuyButtons-ProductForm-' | append: block.id -%}
    <product-form-component
      data-section-id="{{ section.id }}"
      data-product-id="{{ product.id }}"
      data-product-url="{{ product.url }}"
      on:submit="/handleSubmit"
      data-quantity-default="{% if product.selected_or_first_available_variant.quantity_rule.min %}{{ product.selected_or_first_available_variant.quantity_rule.min }}{% else %}1{% endif %}"
    >
      <div
        class="visually-hidden"
        aria-live="assertive"
        role="status"
        aria-atomic="true"
        ref="liveRegion"
      ></div>
      {%- form 'product', product, id: product_form_id, novalidate: 'novalidate', data-type: 'add-to-cart-form' -%}
        <input
          type="hidden"
          name="id"
          ref="variantId"
          value="{{ product.selected_or_first_available_variant.id }}"
        >
        <div
          class="product-form-buttons spacing-style{% if block.settings.stacking %} product-form-buttons--stacked{% endif %}"
          style="{% render 'spacing-style', settings: block.settings %}"
        >
          {% content_for 'block', type: 'quantity', id: 'quantity' %}

          {% content_for 'block',
            type: 'add-to-cart',
            id: 'add-to-cart',
            can_add_to_cart: can_add_to_cart,
            add_to_cart_text: add_to_cart_text
          %}
          <span
            class="product-form-text__error hidden"
            ref="addToCartTextError"
          >
            <span class="svg-wrapper product-form-icon--error">
              {{- 'icon-error.svg' | inline_asset_content -}}
            </span>
          </span>
          {% content_for 'block',
            type: 'accelerated-checkout',
            id: 'accelerated-checkout',
            can_add_to_cart: can_add_to_cart
          %}
        </div>
      {%- endform -%}
    </product-form-component>
  {%- else -%}
    <div class="product-form-buttons">
      <button
        type="submit"
        name="add"
        class="button"
        disabled
      >
        {{ 'blocks.sold_out' | t }}
      </button>
    </div>
  {%- endif -%}
</span>

{% if block.settings.show_pickup_availability %}
  <script
    src="{{ 'local-pickup.js' | asset_url }}"
    type="module"
  ></script>

  {%- assign pick_up_availabilities = closest.product.selected_or_first_available_variant.store_availabilities
    | where: 'pick_up_enabled', true
  -%}

  <local-pickup
    class="spacing-style product__pickup-availabilities"
    {% if pick_up_availabilities.size == 0 %}
      hidden
    {% endif %}
    data-section-id="{{ section.id }}"
    data-product-url="{{ closest.product.url }}"
    data-variant-id="{{ closest.product.selected_or_first_available_variant.id }}"
    style="{% render 'spacing-style', settings: block.settings %}"
    ref="localPickupButton"
  >
    {% if can_add_to_cart %}
      <dialog-component>
        <div class="pickup-availability__row">
          <div class="pickup-availability__column">
            <span class="svg-wrapper">
              {% if pick_up_availabilities.first.available %}
                {{- 'icon-available.svg' | inline_asset_content -}}
              {% else %}
                {{- 'icon-unavailable.svg' | inline_asset_content -}}
              {% endif %}
            </span>
          </div>
          <div class="pickup-availability__column">
            {% if pick_up_availabilities.first.available %}
              <p class="pickup-location__text-sm">
                {{ 'content.pickup_available_at_html' | t: location: pick_up_availabilities.first.location.name }}
              </p>
              <p class="pickup-location__text-xs">
                {{ 'content.pickup_ready_in' | t: pickup_time: pick_up_availabilities.first.pick_up_time }}
              </p>
            {% else %}
              <p class="pickup-location__text-sm">
                {{ 'content.pickup_not_available' | t }}
              </p>
            {% endif %}
            <button
              on:click="/showDialog"
              class="button-unstyled pickup-location__button"
            >
              {{ 'actions.view_store_information' | t }}
            </button>
          </div>
        </div>
        <dialog
          ref="dialog"
          class="dialog-modal dialog-drawer pickup-location__dialog color-{{ settings.drawer_color_scheme }}"
          scroll-lock
        >
          <div class="pickup-availability__dialog-row pickup-availability__header-container">
            <div class="pickup-availability__column">
              <h4 class="pickup-location__h4">{{ closest.product.title }}</h4>
              <p class="pickup-location__text-sm">
                {{ closest.product.selected_or_first_available_variant.title }}
              </p>
            </div>
            <button
              ref="closeButton"
              on:click="/closeDialog"
              class="button button-unstyled pickup-location__close-button"
              aria-label="{{ 'actions.close_dialog' | t }}"
            >
              <span class="svg-wrapper">
                {{- 'icon-close.svg' | inline_asset_content -}}
              </span>
            </button>
          </div>
          {% for pick_up_availability in pick_up_availabilities %}
            <div class="pickup-location__wrapper">
              <p class="pickup-location__text-bold">{{ pick_up_availability.location.name }}</p>
              <div class="pickup-location__address-wrapper">
                <span class="pickup-location__availability-wrapper">
                  {% if pick_up_availability.available %}
                    {{- 'icon-available.svg' | inline_asset_content -}}
                    {% assign pickup_time = pick_up_availability.pick_up_time | downcase %}
                    {{ 'content.pickup_available_in' | t: pickup_time: pickup_time }}
                  {% else %}
                    {{- 'icon-unavailable.svg' | inline_asset_content -}}
                    {{ 'content.pickup_not_available' | t }}
                  {% endif %}
                </span>
                <address class="pickup-location__address">
                  {{ pick_up_availability.location.address | format_address }}
                  {{ pick_up_availability.location.address.phone }}
                </address>
              </div>
            </div>
          {% endfor %}
        </dialog>
      </dialog-component>
    {% endif %}
  </local-pickup>
{% endif %}

{% stylesheet %}
  .buy-buttons-block {
    width: 100%;
  }

  .product-form-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: calc(var(--gap-sm) / 2);

    @media screen and (min-width: 750px) {
      gap: var(--gap-sm);
    }
  }

  .product-form-buttons > *:not(.quantity-selector) {
    flex: 1;
    min-width: 185px;
  }

  .product-form-buttons--stacked > *:not(.quantity-selector) {
    flex-basis: 51%; /* Force the buttons to be on separate rows */
  }

  .quantity-selector {
    flex-grow: 0;
  }

  .product-form-buttons button {
    width: 100%;
    padding-inline: var(--padding-4xl);
    padding-block: var(--padding-lg);
  }

  .add-to-cart-button {
    text-transform: var(--button-text-case-primary);
  }

  .add-to-cart-button.button-secondary {
    text-transform: var(--button-text-case-secondary);
  }

  .product-form-buttons .shopify-payment-button__button {
    width: 100%;
    min-height: var(--minimum-touch-target);
  }

  .quantity-selector,
  .add-to-cart-button {
    height: var(--height-buy-buttons);
  }

  .product__pickup-availabilities {
    width: 100%;
  }

  .pickup-availability__column {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
  }

  .pickup-availability__row {
    display: flex;
    gap: var(--padding-xs);
  }

  .pickup-availability__dialog-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  .pickup-availability__header-container {
    padding-block-end: var(--padding-2xl);
  }

  .pickup-location__wrapper {
    display: flex;
    flex-direction: column;
    padding-block: var(--padding-2xl);
    border-top: 1px solid var(--color-border);
    gap: var(--padding-xs);
  }

  .pickup-location__address-wrapper {
    display: flex;
    flex-direction: column;
    gap: var(--padding-md);
  }

  .pickup-location__dialog {
    padding: var(--padding-2xl);
    position: fixed;
    border-radius: 0;
    width: var(--sidebar-width);
    max-width: 95vw;
    height: 100%;
    margin: 0 0 0 auto;
    border: var(--style-border-drawer);
    box-shadow: var(--shadow-drawer);
    background-color: var(--color-background);
  }

  .pickup-location__dialog:modal {
    max-height: 100dvh;
  }

  .pickup-location__text-sm {
    font-size: var(--font-size--sm);
    margin: 0;
  }

  .pickup-location__text-xs {
    font-size: var(--font-size--xs);
    margin: 0;
  }

  .product-form-text__error {
    display: flex;
    align-items: flex-start;
    gap: var(--gap-xs);
  }

  .pickup-location__button {
    width: fit-content;
    color: var(--color-primary);
    font-size: var(--font-size--xs);
    font-family: var(--font-body--family);
    padding: 0;
    cursor: pointer;
    margin-block: var(--margin-xs);
  }

  .pickup-location__button:hover {
    color: var(--color-primary-hover);
  }

  .pickup-location__h4 {
    margin: 0;
  }

  .pickup-location__text-bold {
    font-size: var(--font-size--md);
    font-weight: 600;
    margin: 0;
  }

  .pickup-location__availability-wrapper {
    display: flex;
    align-items: center;
    gap: var(--gap-xs);
    font-family: var(--font-paragraph--family);
  }

  .pickup-location__address {
    font-style: normal;
  }

  .pickup-location__close-button {
    position: absolute;
    top: calc(var(--padding-2xl) - (var(--icon-size-xs) / 2));
    right: calc(var(--padding-2xl) - var(--icon-size-xs));
    height: var(--minimum-touch-target);
    width: var(--minimum-touch-target);
  }

  .pickup-location__close-button svg {
    width: var(--icon-size-xs);
    height: var(--icon-size-xs);
  }
{% endstylesheet %}

{% schema %}
{
  "name": "t:names.product_buy_buttons",
  "tag": null,
  "settings": [
    {
      "type": "paragraph",
      "content": "t:content.resource_reference_product"
    },
    {
      "type": "checkbox",
      "id": "stacking",
      "label": "t:settings.always_stack_buttons",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_pickup_availability",
      "label": "t:settings.show_pickup_availability",
      "default": true
    },
    {
      "type": "header",
      "content": "t:content.padding"
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "t:settings.top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "t:settings.bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-inline-start",
      "label": "t:settings.left",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-inline-end",
      "label": "t:settings.right",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "t:names.product_buy_buttons",
      "category": "t:categories.product",
      "blocks": {
        "quantity": {
          "type": "quantity",
          "static": true
        },
        "add-to-cart": {
          "type": "add-to-cart",
          "static": true,
          "settings": {
            "style_class": "button-secondary"
          }
        },
        "accelerated-checkout": {
          "type": "accelerated-checkout",
          "static": true
        }
      }
    }
  ]
}
{% endschema %}
