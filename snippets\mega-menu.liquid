{%- doc -%}
  Renders a mega menu list and optional "more" menu links.
  When more menu links are present a duplicate instance of each submenu is also rendered.

  @param {object} [section] - The section object.
  @param {linklist} [parent_link] - The linklist to render
  @param {string} [id] - Unique ID to assign ul in markup
  @param {string} [menu_content_type] - The type of content to render, options: ['featured_products', 'featured_collections', 'collection_images', 'text'] (default: 'text')
  @param {number} [content_aspect_ratio] - The aspect ratio to display the content if applicable
  @param {number} [image_border_radius] - The border radius used for the content images
{%- enddoc -%}

<div class="mega-menu section section--full-width-margin section--{{ section.settings.section_width }}">
  <div
    class="mega-menu__grid"
    data-menu-grid-id="{{ id }}"
  >
    {% render 'mega-menu-list',
      parent_link: parent_link,
      id: id,
      grid_columns_count: 6,
      grid_columns_count_tablet: 4,
      grid_columns_count_collection_images: 8,
      menu_content_type: menu_content_type,
      content_aspect_ratio: content_aspect_ratio,
      image_border_radius: image_border_radius
    %}
  </div>
</div>
