{% style %}
  {% for scheme in settings.color_schemes -%}
    {% assign scheme_classes = scheme_classes | append: ', .color-' | append: scheme.id %}
    {% if forloop.index == 1 %}
      :root,
    {% endif %}
    {% assign background_brightness = scheme.settings.background | color_brightness %}
    {% if background_brightness < 64 %}
      {% assign opacity_5_15 = 0.15 %}
      {% assign opacity_10_25 = 0.25 %}
      {% assign opacity_35_55 = 0.55 %}
      {% assign opacity_40_60 = 0.60 %}
      {% assign opacity_30_60 = 0.60 %}
    {% else %}
      {% assign opacity_5_15 = 0.05 %}
      {% assign opacity_10_25 = 0.1 %}
      {% assign opacity_35_55 = 0.35 %}
      {% assign opacity_40_60 = 0.40 %}
      {% assign opacity_30_60 = 0.30 %}
    {% endif %}
    .color-{{ scheme.id }} {
        --color-background: rgba({{ scheme.settings.background.rgba }});
        --opacity-5-15: {{ opacity_5_15 }};
        --opacity-10-25: {{ opacity_10_25 }};
        --opacity-35-55: {{ opacity_35_55 }};
        --opacity-40-60: {{ opacity_40_60 }};
        --opacity-30-60: {{ opacity_30_60 }};
        --color-foreground: rgba({{ scheme.settings.foreground.rgba }});
        --color-foreground-heading: rgba({{ scheme.settings.foreground_heading.rgba }});
        --color-primary: rgba({{ scheme.settings.primary.rgba }});
        --color-primary-hover: rgba({{ scheme.settings.primary_hover.rgba }});
        --color-border: rgba({{ scheme.settings.border.rgba }});
        --color-shadow: rgba({{ scheme.settings.shadow.rgba }});
        --color-primary-button-text: rgba({{ scheme.settings.primary_button_text.rgba }});
        --color-primary-button-background: rgba({{ scheme.settings.primary_button_background.rgba }});
        --color-primary-button-border: rgba({{ scheme.settings.primary_button_border.rgba }});
        --color-primary-button-hover-text: rgba({{ scheme.settings.primary_button_hover_text.rgba }});
        --color-primary-button-hover-background: rgba({{ scheme.settings.primary_button_hover_background.rgba }});
        --color-primary-button-hover-border: rgba({{ scheme.settings.primary_button_hover_border.rgba }});
        --color-secondary-button-text: rgba({{ scheme.settings.secondary_button_text.rgba }});
        --color-secondary-button-background: rgba({{ scheme.settings.secondary_button_background.rgba }});
        --color-secondary-button-border: rgba({{ scheme.settings.secondary_button_border.rgba }});
        --color-secondary-button-hover-text: rgba({{ scheme.settings.secondary_button_hover_text.rgba }});
        --color-secondary-button-hover-background: rgba({{ scheme.settings.secondary_button_hover_background.rgba }});
        --color-secondary-button-hover-border: rgba({{ scheme.settings.secondary_button_hover_border.rgba }});
        --color-input-background: rgba({{ scheme.settings.input_background.rgba }});
        --color-input-text: rgba({{ scheme.settings.input_text_color.rgba }});
        --color-input-border: rgba({{ scheme.settings.input_border_color.rgba }});
        --color-input-hover-background: rgba({{ scheme.settings.input_hover_background.rgba }});
        --color-variant-background: rgba({{ scheme.settings.variant_background_color.rgba }});
        --color-variant-border: rgba({{ scheme.settings.variant_border_color.rgba }});
        --color-variant-text: rgba({{ scheme.settings.variant_text_color.rgba }});
        --color-variant-hover-background: rgba({{ scheme.settings.variant_hover_background_color.rgba }});
        --color-variant-hover-text: rgba({{ scheme.settings.variant_hover_text_color.rgba }});
        --color-variant-hover-border: rgba({{ scheme.settings.variant_hover_border_color.rgba }});
        --color-selected-variant-background: rgba({{ scheme.settings.selected_variant_background_color.rgba }});
        --color-selected-variant-border: rgba({{ scheme.settings.selected_variant_border_color.rgba }});
        --color-selected-variant-text: rgba({{ scheme.settings.selected_variant_text_color.rgba }});
        --color-selected-variant-hover-background: rgba({{ scheme.settings.selected_variant_hover_background_color.rgba }});
        --color-selected-variant-hover-text: rgba({{ scheme.settings.selected_variant_hover_text_color.rgba }});
        --color-selected-variant-hover-border: rgba({{ scheme.settings.selected_variant_hover_border_color.rgba }});
        --input-disabled-background-color: rgb(from var(--color-foreground) r g b / 10%);
        --input-disabled-border-color: rgb(from var(--color-foreground) r g b / var(--opacity-5-15));
        --input-disabled-text-color: rgb(from var(--color-foreground) r g b / 50%);
        --color-foreground-muted: rgb(from var(--color-foreground) r g b / 60%);
        --font-h1--color: var(--color-foreground-heading);
        --font-h2--color: var(--color-foreground-heading);
        --font-h3--color: var(--color-foreground-heading);
        --font-h4--color: var(--color-foreground-heading);
        --font-h5--color: var(--color-foreground-heading);
        --font-h6--color: var(--color-foreground-heading);

        /* Shadows */
        {% if settings.drawer_drop_shadow %}
          --shadow-drawer: 0px 4px 20px rgb(from var(--color-shadow) r g b / 15%);
        {% endif %}
        {% if settings.popover_drop_shadow %}
          --shadow-blur: 20px;
          --shadow-popover: 0px 4px 20px rgb(from var(--color-shadow) r g b / 15%);
        {% endif %}
      }
  {% endfor %}

  {{ scheme_classes | prepend: 'body' }} {
    color: var(--color-foreground);
    background-color: var(--color-background);
  }
{% endstyle %}
