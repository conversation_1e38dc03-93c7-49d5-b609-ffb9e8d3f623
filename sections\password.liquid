{% # import schema from '../schemas/sections/password' %}

<div class="section-background color-{{ section.settings.color_scheme }}"></div>
<div
  class="section section--password section--{{ section.settings.section_width }} color-{{ section.settings.color_scheme }}"
>
  <div
    class="border-style"
    style="{% render 'border-override', settings: section.settings %}"
  >
    {% render 'background-media',
      background_media: section.settings.background_media,
      background_video: section.settings.video,
      background_video_position: section.settings.video_position,
      background_image: section.settings.background_image,
      background_image_position: section.settings.background_image_position
    %}

    <div
      class="
        spacing-style
        layout-panel-flex
        layout-panel-flex--{{ section.settings.content_direction }}
        section-content-wrapper
        {% if section.settings.vertical_on_mobile %} mobile-column{% endif %}
      "
      style="
        {% render 'layout-panel-style', settings: section.settings %}
        {%  render 'spacing-style', settings: section.settings %}
      "
      data-testid="section-content"
    >
      {% content_for 'blocks' %}
    </div>
  </div>
</div>

{% stylesheet %}
  .section--password {
    --section-min-height: calc(100svh - 8rem);
  }

  .password-enter {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--gap-sm);
    padding-block: var(--padding-xl);
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: var(--layer-overlay);
  }

  .password-enter__powered-by {
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--padding-xs);
    height: 1em;

    a {
      display: flex;
    }

    svg {
      display: inline;
      height: 1.3em;
      color: var(--color-foreground);
    }
  }

  .password-enter__links {
    display: flex;
    align-items: center;
    gap: var(--gap-2xl);

    @media screen and (max-width: 749px) {
      flex-direction: column;
      gap: var(--gap-sm);
    }
  }

  .password-enter__admin-link {
    margin: 0;
  }

  /* Adjust padding when preview bar is present to ensure button visibility */
  body:has(#PBarNextFrameWrapper) .password-enter {
    padding-bottom: var(--padding-6xl);
  }

  .password-enter__button {
    height: var(--minimum-touch-target);
    background-color: transparent;
    color: var(--color-primary);
    cursor: pointer;
    text-decoration: underline;

    &:hover {
      color: var(--color-primary-hover);
      text-decoration: none;
    }
  }

  .password-dialog {
    max-width: 100vw;
    max-height: 100svh;
    width: 100vw;
    height: 100svh;
    border: none;
    transition: opacity var(--animation-values), display var(--animation-speed) allow-discrete,
      overlay var(--animation-speed) allow-discrete;

    &::backdrop {
      display: none;
    }
  }

  .password-dialog[open] {
    opacity: 1;
    top: 0;
    left: 0;
  }

  @starting-style {
    .password-dialog[open] {
      opacity: 0;
    }
  }

  .password-dialog:not([open]) {
    opacity: 0;
  }

  @starting-style {
    .password-dialog:not([open]) {
      opacity: 1;
    }
  }

  .password-dialog__close-button {
    cursor: pointer;
    width: var(--minimum-touch-target);
    height: var(--minimum-touch-target);
  }

  .password-dialog__header {
    position: absolute;
    top: 0;
    right: 0;
    padding: var(--padding-lg);
    z-index: var(--layer-raised);
  }

  .password-dialog__content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: var(--padding-lg);
    height: 100%;
    transition: transform var(--animation-values);
  }

  .password-dialog__content .email-signup__message {
    padding-block-start: var(--padding-xl);
  }

  .password-dialog[open] .password-dialog__content {
    transform: translateY(0);
  }

  @starting-style {
    .password-dialog[open] .password-dialog__content {
      transform: translateY(1em);
    }
  }

  .password-dialog:not([open]) .password-dialog__content {
    transform: translateY(1em);
  }

  @starting-style {
    .password-dialog:not([open]) .password-dialog__content {
      transform: translateY(0);
    }
  }

  .storefront-password-form {
    max-width: 400px;
    width: 100%;
  }

  .email-signup__input-group,
  .password-dialog__submit-button {
    width: 100%;

    @media screen and (min-width: 750px) {
      width: auto;
    }
  }
{% endstylesheet %}

{% schema %}
{
  "name": "t:names.section",
  "class": "section-wrapper",
  "blocks": [
    {
      "type": "@theme"
    },
    {
      "type": "@app"
    },
    {
      "type": "_divider"
    }
  ],
  "disabled_on": {
    "groups": ["header"]
  },
  "settings": [
    {
      "type": "header",
      "content": "t:content.layout"
    },
    {
      "type": "select",
      "id": "content_direction",
      "label": "t:settings.direction",
      "options": [
        {
          "value": "column",
          "label": "t:options.vertical"
        },
        {
          "value": "row",
          "label": "t:options.horizontal"
        }
      ],
      "default": "column"
    },
    {
      "type": "checkbox",
      "id": "vertical_on_mobile",
      "label": "t:settings.vertical_on_mobile",
      "default": true,
      "visible_if": "{{ section.settings.content_direction == 'row' }}"
    },
    {
      "type": "select",
      "id": "horizontal_alignment",
      "label": "t:settings.alignment",
      "options": [
        {
          "value": "flex-start",
          "label": "t:options.left"
        },
        {
          "value": "center",
          "label": "t:options.center"
        },
        {
          "value": "flex-end",
          "label": "t:options.right"
        },
        {
          "value": "space-between",
          "label": "t:options.space_between"
        }
      ],
      "default": "flex-start",
      "visible_if": "{{ section.settings.content_direction == 'row' }}"
    },
    {
      "type": "select",
      "id": "vertical_alignment",
      "label": "t:settings.position",
      "options": [
        {
          "value": "flex-start",
          "label": "t:options.top"
        },
        {
          "value": "center",
          "label": "t:options.center"
        },
        {
          "value": "flex-end",
          "label": "t:options.bottom"
        }
      ],
      "default": "center",
      "visible_if": "{{ section.settings.content_direction == 'row' }}"
    },
    {
      "type": "checkbox",
      "id": "align_baseline",
      "label": "t:settings.align_baseline",
      "default": false,
      "visible_if": "{{ section.settings.vertical_alignment == 'flex-end' }}"
    },
    {
      "type": "select",
      "id": "horizontal_alignment_flex_direction_column",
      "label": "t:settings.alignment",
      "options": [
        {
          "value": "flex-start",
          "label": "t:options.left"
        },
        {
          "value": "center",
          "label": "t:options.center"
        },
        {
          "value": "flex-end",
          "label": "t:options.right"
        }
      ],
      "default": "flex-start",
      "visible_if": "{{ section.settings.content_direction != 'row' }}"
    },
    {
      "type": "select",
      "id": "vertical_alignment_flex_direction_column",
      "label": "t:settings.position",
      "options": [
        {
          "value": "flex-start",
          "label": "t:options.top"
        },
        {
          "value": "center",
          "label": "t:options.center"
        },
        {
          "value": "flex-end",
          "label": "t:options.bottom"
        },
        {
          "value": "space-between",
          "label": "t:options.space_between"
        }
      ],
      "default": "center",
      "visible_if": "{{ section.settings.content_direction == 'column' }}"
    },
    {
      "type": "range",
      "id": "gap",
      "label": "t:settings.gap",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 12
    },
    {
      "type": "header",
      "content": "t:content.size"
    },
    {
      "type": "select",
      "id": "section_width",
      "label": "t:settings.width",
      "options": [
        {
          "value": "page-width",
          "label": "t:options.page"
        },
        {
          "value": "full-width",
          "label": "t:options.full"
        }
      ],
      "default": "page-width"
    },
    {
      "type": "header",
      "content": "t:content.appearance"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:settings.color_scheme",
      "default": "scheme-1"
    },
    {
      "type": "select",
      "id": "background_media",
      "label": "t:settings.background_media",
      "options": [
        {
          "value": "none",
          "label": "t:options.none"
        },
        {
          "value": "image",
          "label": "t:options.image"
        },
        {
          "value": "video",
          "label": "t:options.video"
        }
      ],
      "default": "none"
    },
    {
      "type": "video",
      "id": "video",
      "label": "t:settings.video",
      "visible_if": "{{ section.settings.background_media == 'video' }}"
    },
    {
      "type": "select",
      "id": "video_position",
      "label": "t:settings.video_position",
      "options": [
        {
          "value": "cover",
          "label": "t:options.cover"
        },
        {
          "value": "contain",
          "label": "t:options.contain"
        }
      ],
      "default": "cover",
      "visible_if": "{{ section.settings.background_media == 'video' }}"
    },
    {
      "type": "image_picker",
      "id": "background_image",
      "label": "t:settings.image",
      "visible_if": "{{ section.settings.background_media == 'image' }}"
    },
    {
      "type": "select",
      "id": "background_image_position",
      "label": "t:settings.image_position",
      "options": [
        {
          "value": "cover",
          "label": "t:options.cover"
        },
        {
          "value": "fit",
          "label": "t:options.fit"
        }
      ],
      "default": "cover",
      "visible_if": "{{ section.settings.background_media == 'image' }}"
    },
    {
      "type": "select",
      "id": "border",
      "label": "t:settings.borders",
      "options": [
        {
          "value": "none",
          "label": "t:options.none"
        },
        {
          "value": "solid",
          "label": "t:options.solid"
        }
      ],
      "default": "none"
    },
    {
      "type": "range",
      "id": "border_width",
      "min": 0,
      "max": 10,
      "step": 0.5,
      "unit": "px",
      "label": "t:settings.border_width",
      "default": 1,
      "visible_if": "{{ section.settings.border != 'none' }}"
    },
    {
      "type": "range",
      "id": "border_opacity",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "%",
      "label": "t:settings.border_opacity",
      "default": 100,
      "visible_if": "{{ section.settings.border != 'none' }}"
    },
    {
      "type": "range",
      "id": "border_radius",
      "label": "t:settings.border_radius",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "header",
      "content": "t:content.padding"
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "t:settings.top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "t:settings.bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    }
  ]
}
{% endschema %}
