{% # import schema from '../schemas/sections/section' %}

{% capture children %}
  {% content_for 'blocks' %}
{% endcapture %}

{% render 'section', section: section, children: children %}

{% schema %}
{
  "name": "t:names.section",
  "class": "section-wrapper",
  "blocks": [
    {
      "type": "@theme"
    },
    {
      "type": "@app"
    },
    {
      "type": "_divider"
    }
  ],
  "disabled_on": {
    "groups": ["header"]
  },
  "settings": [
    {
      "type": "header",
      "content": "t:content.layout"
    },
    {
      "type": "select",
      "id": "content_direction",
      "label": "t:settings.direction",
      "options": [
        {
          "value": "column",
          "label": "t:options.vertical"
        },
        {
          "value": "row",
          "label": "t:options.horizontal"
        }
      ],
      "default": "column"
    },
    {
      "type": "checkbox",
      "id": "vertical_on_mobile",
      "label": "t:settings.vertical_on_mobile",
      "default": true,
      "visible_if": "{{ section.settings.content_direction == 'row' }}"
    },
    {
      "type": "select",
      "id": "horizontal_alignment",
      "label": "t:settings.alignment",
      "options": [
        {
          "value": "flex-start",
          "label": "t:options.left"
        },
        {
          "value": "center",
          "label": "t:options.center"
        },
        {
          "value": "flex-end",
          "label": "t:options.right"
        },
        {
          "value": "space-between",
          "label": "t:options.space_between"
        }
      ],
      "default": "flex-start",
      "visible_if": "{{ section.settings.content_direction == 'row' }}"
    },
    {
      "type": "select",
      "id": "vertical_alignment",
      "label": "t:settings.position",
      "options": [
        {
          "value": "flex-start",
          "label": "t:options.top"
        },
        {
          "value": "center",
          "label": "t:options.center"
        },
        {
          "value": "flex-end",
          "label": "t:options.bottom"
        }
      ],
      "default": "center",
      "visible_if": "{{ section.settings.content_direction == 'row' }}"
    },
    {
      "type": "checkbox",
      "id": "align_baseline",
      "label": "t:settings.align_baseline",
      "default": false,
      "visible_if": "{{ section.settings.vertical_alignment == 'flex-end' }}"
    },
    {
      "type": "select",
      "id": "horizontal_alignment_flex_direction_column",
      "label": "t:settings.alignment",
      "options": [
        {
          "value": "flex-start",
          "label": "t:options.left"
        },
        {
          "value": "center",
          "label": "t:options.center"
        },
        {
          "value": "flex-end",
          "label": "t:options.right"
        }
      ],
      "default": "flex-start",
      "visible_if": "{{ section.settings.content_direction != 'row' }}"
    },
    {
      "type": "select",
      "id": "vertical_alignment_flex_direction_column",
      "label": "t:settings.position",
      "options": [
        {
          "value": "flex-start",
          "label": "t:options.top"
        },
        {
          "value": "center",
          "label": "t:options.center"
        },
        {
          "value": "flex-end",
          "label": "t:options.bottom"
        },
        {
          "value": "space-between",
          "label": "t:options.space_between"
        }
      ],
      "default": "center",
      "visible_if": "{{ section.settings.content_direction == 'column' }}"
    },
    {
      "type": "range",
      "id": "gap",
      "label": "t:settings.gap",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 12
    },
    {
      "type": "header",
      "content": "t:content.size"
    },
    {
      "type": "select",
      "id": "section_width",
      "label": "t:settings.width",
      "options": [
        {
          "value": "page-width",
          "label": "t:options.page"
        },
        {
          "value": "full-width",
          "label": "t:options.full"
        }
      ],
      "default": "page-width"
    },
    {
      "type": "select",
      "id": "section_height",
      "label": "t:settings.height",
      "options": [
        {
          "value": "",
          "label": "t:options.auto"
        },
        {
          "value": "small",
          "label": "t:options.small"
        },
        {
          "value": "medium",
          "label": "t:options.medium"
        },
        {
          "value": "large",
          "label": "t:options.large"
        },
        {
          "value": "full-screen",
          "label": "t:options.full_screen"
        },
        {
          "value": "custom",
          "label": "t:options.custom"
        }
      ],
      "default": ""
    },
    {
      "type": "range",
      "id": "section_height_custom",
      "label": "t:settings.custom_height",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 50,
      "unit": "%",
      "visible_if": "{{ section.settings.section_height == 'custom' }}"
    },
    {
      "type": "header",
      "content": "t:content.appearance"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:settings.color_scheme",
      "default": "scheme-1"
    },
    {
      "type": "select",
      "id": "background_media",
      "label": "t:settings.background_media",
      "options": [
        {
          "value": "none",
          "label": "t:options.none"
        },
        {
          "value": "image",
          "label": "t:options.image"
        },
        {
          "value": "video",
          "label": "t:options.video"
        }
      ],
      "default": "none"
    },
    {
      "type": "video",
      "id": "video",
      "label": "t:settings.video",
      "visible_if": "{{ section.settings.background_media == 'video' }}"
    },
    {
      "type": "select",
      "id": "video_position",
      "label": "t:settings.video_position",
      "options": [
        {
          "value": "cover",
          "label": "t:options.cover"
        },
        {
          "value": "contain",
          "label": "t:options.contain"
        }
      ],
      "default": "cover",
      "visible_if": "{{ section.settings.background_media == 'video' }}"
    },
    {
      "type": "image_picker",
      "id": "background_image",
      "label": "t:settings.image",
      "visible_if": "{{ section.settings.background_media == 'image' }}"
    },
    {
      "type": "select",
      "id": "background_image_position",
      "label": "t:settings.image_position",
      "options": [
        {
          "value": "cover",
          "label": "t:options.cover"
        },
        {
          "value": "fit",
          "label": "t:options.fit"
        }
      ],
      "default": "cover",
      "visible_if": "{{ section.settings.background_media == 'image' }}"
    },
    {
      "type": "select",
      "id": "border",
      "label": "t:settings.borders",
      "options": [
        {
          "value": "none",
          "label": "t:options.none"
        },
        {
          "value": "solid",
          "label": "t:options.solid"
        }
      ],
      "default": "none"
    },
    {
      "type": "range",
      "id": "border_width",
      "min": 0,
      "max": 10,
      "step": 0.5,
      "unit": "px",
      "label": "t:settings.border_width",
      "default": 1,
      "visible_if": "{{ section.settings.border != 'none' }}"
    },
    {
      "type": "range",
      "id": "border_opacity",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "%",
      "label": "t:settings.border_opacity",
      "default": 100,
      "visible_if": "{{ section.settings.border != 'none' }}"
    },
    {
      "type": "range",
      "id": "border_radius",
      "label": "t:settings.border_radius",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "checkbox",
      "id": "toggle_overlay",
      "label": "t:settings.background_overlay"
    },
    {
      "type": "color",
      "id": "overlay_color",
      "label": "t:settings.overlay_color",
      "alpha": true,
      "default": "#00000026",
      "visible_if": "{{ section.settings.toggle_overlay }}"
    },
    {
      "type": "select",
      "id": "overlay_style",
      "label": "t:settings.overlay_style",
      "options": [
        {
          "value": "solid",
          "label": "t:options.solid"
        },
        {
          "value": "gradient",
          "label": "t:options.gradient"
        }
      ],
      "default": "solid",
      "visible_if": "{{ section.settings.toggle_overlay }}"
    },
    {
      "type": "select",
      "id": "gradient_direction",
      "label": "t:settings.gradient_direction",
      "options": [
        {
          "value": "to top",
          "label": "t:options.up"
        },
        {
          "value": "to bottom",
          "label": "t:options.down"
        }
      ],
      "default": "to top",
      "visible_if": "{{ section.settings.toggle_overlay and section.settings.overlay_style == 'gradient' }}"
    },
    {
      "type": "header",
      "content": "t:content.padding"
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "t:settings.top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "t:settings.bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "t:names.custom_section"
    },
    {
      "name": "t:names.faq_section",
      "category": "t:categories.storytelling",
      "blocks": {
        "text": {
          "type": "text",
          "name": "t:names.heading",
          "settings": {
            "text": "<h3>Frequently asked questions</h3>",
            "type_preset": "rte",
            "font": "var(--font-body--family)",
            "font_size": "",
            "line_height": "normal",
            "letter_spacing": "normal",
            "case": "none",
            "wrap": "pretty",
            "width": "fit-content",
            "max_width": "narrow",
            "alignment": "left",
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          }
        },
        "accordion": {
          "type": "accordion",
          "name": "t:names.accordion",
          "settings": {
            "icon": "caret",
            "dividers": true,
            "type_preset": "h5",
            "inherit_color_scheme": true,
            "color_scheme": "",
            "border": "none",
            "border_width": 1,
            "border_opacity": 100,
            "border_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {
            "accordion_row_1": {
              "type": "_accordion-row",
              "settings": {
                "heading": "What is the return policy?"
              },
              "blocks": {
                "text": {
                  "type": "text",
                  "settings": {
                    "text": "<p>Our goal is for every customer to be totally satisfied with their purchase. If this isn’t the case, let us know and we’ll do our best to work with you to make it right. </p>",
                    "type_preset": "rte",
                    "font": "var(--font-body--family)",
                    "font_size": "",
                    "line_height": "normal",
                    "letter_spacing": "normal",
                    "case": "none",
                    "wrap": "pretty",
                    "width": "100%",
                    "max_width": "normal",
                    "alignment": "left",
                    "padding-block-start": 0,
                    "padding-block-end": 0,
                    "padding-inline-start": 0,
                    "padding-inline-end": 0
                  }
                }
              },
              "block_order": ["text"]
            },
            "accordion_row_2": {
              "type": "_accordion-row",
              "settings": {
                "heading": "Are any purchases final sale?"
              },
              "blocks": {
                "text": {
                  "type": "text",
                  "settings": {
                    "text": "<p>We are unable to accept returns on certain items. These will be carefully marked before purchase.</p>",
                    "type_preset": "rte",
                    "font": "var(--font-body--family)",
                    "font_size": "",
                    "line_height": "normal",
                    "letter_spacing": "normal",
                    "case": "none",
                    "wrap": "pretty",
                    "width": "100%",
                    "max_width": "normal",
                    "alignment": "left",
                    "padding-block-start": 0,
                    "padding-block-end": 0,
                    "padding-inline-start": 0,
                    "padding-inline-end": 0
                  }
                }
              },
              "block_order": ["text"]
            },
            "accordion_row_3": {
              "type": "_accordion-row",
              "name": "t:names.accordion_dash_row",
              "settings": {
                "heading": "When will I get my order?"
              },
              "blocks": {
                "text": {
                  "type": "text",
                  "name": "t:names.text",
                  "settings": {
                    "text": "<p>We will work quickly to ship your order as soon as possible. Once your order has shipped, you will receive an email with further information. Delivery times vary depending on your location.</p>",
                    "type_preset": "rte",
                    "font": "var(--font-body--family)",
                    "font_size": "",
                    "line_height": "normal",
                    "letter_spacing": "normal",
                    "case": "none",
                    "wrap": "pretty",
                    "width": "100%",
                    "max_width": "normal",
                    "alignment": "left",
                    "padding-block-start": 0,
                    "padding-block-end": 0,
                    "padding-inline-start": 0,
                    "padding-inline-end": 0
                  }
                }
              },
              "block_order": ["text"]
            },
            "accordion_row_4": {
              "type": "_accordion-row",
              "name": "t:names.accordion_dash_row",
              "settings": {
                "heading": "Where are your products manufactured?"
              },
              "blocks": {
                "text": {
                  "type": "text",
                  "name": "t:names.text",
                  "settings": {
                    "text": "<p>Our products are manufactured both locally and globally. We carefully select our manufacturing partners to ensure our products are high quality and a fair value. </p>",
                    "type_preset": "rte",
                    "font": "var(--font-body--family)",
                    "font_size": "",
                    "line_height": "normal",
                    "letter_spacing": "normal",
                    "case": "none",
                    "wrap": "pretty",
                    "width": "100%",
                    "max_width": "normal",
                    "alignment": "left",
                    "padding-block-start": 0,
                    "padding-block-end": 0,
                    "padding-inline-start": 0,
                    "padding-inline-end": 0
                  }
                }
              },
              "block_order": ["text"]
            },
            "accordion_row_5": {
              "type": "_accordion-row",
              "name": "t:names.accordion_dash_row",
              "settings": {
                "heading": "How much does shipping cost?"
              },
              "blocks": {
                "text": {
                  "type": "text",
                  "name": "t:names.text",
                  "settings": {
                    "text": "<p>Shipping is calculated based on your location and the items in your order. You will always know the shipping price before you purchase.</p>",
                    "type_preset": "rte",
                    "font": "var(--font-body--family)",
                    "font_size": "",
                    "line_height": "normal",
                    "letter_spacing": "normal",
                    "case": "none",
                    "wrap": "pretty",
                    "width": "100%",
                    "max_width": "normal",
                    "alignment": "left",
                    "padding-block-start": 0,
                    "padding-block-end": 0,
                    "padding-inline-start": 0,
                    "padding-inline-end": 0
                  }
                }
              },
              "block_order": ["text"]
            }
          },
          "block_order": ["accordion_row_1", "accordion_row_2", "accordion_row_3", "accordion_row_4", "accordion_row_5"]
        }
      },
      "block_order": ["text", "accordion"],
      "settings": {
        "content_direction": "column",
        "vertical_on_mobile": true,
        "horizontal_alignment": "flex-start",
        "vertical_alignment": "center",
        "horizontal_alignment_flex_direction_column": "flex-start",
        "vertical_alignment_flex_direction_column": "center",
        "gap": 32,
        "section_width": "page-width",
        "section_height": "",
        "color_scheme": "",
        "background_media": "none",
        "video_position": "cover",
        "background_image_position": "cover",
        "border": "none",
        "border_width": 1,
        "border_opacity": 100,
        "border_radius": 0,
        "padding-block-start": 48,
        "padding-block-end": 48
      }
    },
    {
      "name": "t:names.video_section",
      "category": "t:categories.storytelling",
      "settings": {
        "content_direction": "column",
        "vertical_on_mobile": true,
        "horizontal_alignment": "flex-start",
        "vertical_alignment": "center",
        "horizontal_alignment_flex_direction_column": "flex-start",
        "vertical_alignment_flex_direction_column": "center",
        "gap": 16,
        "section_width": "page-width",
        "section_height": "",
        "color_scheme": "",
        "background_media": "none",
        "video_position": "cover",
        "background_image_position": "cover",
        "border": "none",
        "border_width": 1,
        "border_opacity": 100,
        "border_radius": 0,
        "padding-block-start": 32,
        "padding-block-end": 32
      },
      "blocks": {
        "video": {
          "type": "video",
          "name": "t:names.video",
          "settings": {
            "video": "",
            "custom_width": 100,
            "custom_width_mobile": 100,
            "aspect_ratio": "16/9",
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          }
        },
        "group": {
          "type": "group",
          "name": "Caption",
          "settings": {
            "content_direction": "row",
            "vertical_on_mobile": true,
            "horizontal_alignment": "space-between",
            "vertical_alignment": "flex-end",
            "align_baseline": true,
            "horizontal_alignment_flex_direction_column": "flex-start",
            "vertical_alignment_flex_direction_column": "center",
            "gap": 12,
            "width": "fill",
            "custom_width": 100,
            "width_mobile": "fill",
            "custom_width_mobile": 100,
            "height": "fit",
            "custom_height": 100,
            "inherit_color_scheme": true,
            "color_scheme": "",
            "background_media": "none",
            "video_position": "cover",
            "background_image_position": "cover",
            "border": "none",
            "border_width": 1,
            "border_opacity": 100,
            "border_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {
            "text": {
              "type": "text",
              "name": "t:names.text",
              "settings": {
                "text": "<p>Take a look behind the scenes of our latest product launch.</p>",
                "type_preset": "rte",
                "font": "var(--font-body--family)",
                "font_size": "",
                "line_height": "normal",
                "letter_spacing": "normal",
                "case": "none",
                "wrap": "pretty",
                "width": "fit-content",
                "max_width": "normal",
                "alignment": "left",
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              }
            },
            "button": {
              "type": "button",
              "name": "t:names.button",
              "settings": {
                "label": "Discover the collection",
                "link": "shopify://collections/all",
                "open_in_new_tab": false,
                "style_class": "link",
                "width": "fit-content",
                "custom_width": 100,
                "width_mobile": "fit-content",
                "custom_width_mobile": 100
              }
            }
          },
          "block_order": ["text", "button"]
        }
      },
      "block_order": ["video", "group"]
    },
    {
      "name": "t:names.pull_quote",
      "category": "t:categories.storytelling",
      "blocks": {
        "text": {
          "type": "text",
          "settings": {
            "text": "<p>At the heart of every product lies a unique story, driven by our passion for quality and innovation. Each item enhances your everyday life and sparks joy.</p>",
            "type_preset": "h2",
            "width": "100%",
            "max_width": "narrow",
            "alignment": "center"
          }
        },
        "button": {
          "type": "button",
          "settings": {
            "label": "Shop now",
            "link": "shopify://collections/all",
            "style_class": "link"
          }
        }
      },
      "block_order": ["text", "button"],
      "settings": {
        "horizontal_alignment_flex_direction_column": "center",
        "gap": 16,
        "padding-block-start": 64,
        "padding-block-end": 64
      }
    },
    {
      "name": "t:names.contact_form",
      "category": "t:categories.forms",
      "blocks": {
        "text": {
          "type": "text",
          "settings": {
            "text": "<h2>Contact us</h2>",
            "type_preset": "rte",
            "font": "var(--font-body--family)",
            "font_size": "",
            "line_height": "normal",
            "letter_spacing": "normal",
            "case": "none",
            "wrap": "pretty",
            "width": "100%",
            "max_width": "normal",
            "alignment": "center",
            "padding-block-start": 0,
            "padding-block-end": 0
          }
        },
        "contact_form": {
          "type": "contact-form",
          "settings": {
            "width": "custom",
            "custom_width": 50,
            "width_mobile": "custom",
            "custom_width_mobile": 100,
            "inherit_color_scheme": true,
            "color_scheme": "",
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {
            "submit-button": {
              "type": "contact-form-submit-button",
              "static": true,
              "settings": {
                "label": "Submit",
                "style_class": "button",
                "width": "fit-content",
                "custom_width": 100,
                "width_mobile": "fit-content",
                "custom_width_mobile": 100
              }
            }
          },
          "block_order": []
        }
      },
      "block_order": ["text", "contact_form"],
      "settings": {
        "content_direction": "column",
        "vertical_on_mobile": true,
        "horizontal_alignment": "flex-start",
        "vertical_alignment": "center",
        "horizontal_alignment_flex_direction_column": "center",
        "vertical_alignment_flex_direction_column": "center",
        "gap": 32,
        "section_width": "page-width",
        "section_height": "",
        "color_scheme": "scheme-1",
        "background_media": "none",
        "video_position": "cover",
        "background_image_position": "cover",
        "border": "none",
        "border_width": 1,
        "border_opacity": 100,
        "border_radius": 0,
        "padding-block-start": 32,
        "padding-block-end": 32
      }
    },
    {
      "name": "t:names.email_signup",
      "category": "t:categories.forms",
      "settings": {
        "horizontal_alignment_flex_direction_column": "center",
        "gap": 16,
        "color_scheme": "scheme-2",
        "padding-block-start": 40,
        "padding-block-end": 40
      },
      "blocks": {
        "text_1": {
          "type": "text",
          "name": "t:names.heading",
          "settings": {
            "text": "<h2>Subscribe to our emails</h2>",
            "width": "100%",
            "alignment": "center"
          }
        },
        "text_2": {
          "type": "text",
          "settings": {
            "text": "<p>Be the first to know about new collections and special offers. </p>",
            "type_preset": "paragraph",
            "width": "100%",
            "alignment": "center"
          }
        },
        "email_signup": {
          "type": "email-signup",
          "settings": {
            "width": "custom",
            "custom_width": 50,
            "style_class": "button-unstyled",
            "display_type": "arrow",
            "label": "Sign Up",
            "integrated_button": true
          }
        }
      },
      "block_order": ["text_1", "text_2", "email_signup"]
    },
    {
      "name": "t:names.icons_with_text",
      "category": "t:categories.storytelling",
      "blocks": {
        "group_1": {
          "type": "group",
          "settings": {
            "content_direction": "column",
            "vertical_on_mobile": true,
            "horizontal_alignment": "flex-start",
            "vertical_alignment": "center",
            "horizontal_alignment_flex_direction_column": "center",
            "vertical_alignment_flex_direction_column": "center",
            "gap": 16,
            "height": "fit",
            "inherit_color_scheme": true
          },
          "blocks": {
            "icon": {
              "type": "icon",
              "settings": {
                "icon": "eye",
                "width": 32,
                "link": ""
              }
            },
            "group": {
              "type": "group",
              "settings": {
                "gap": 4
              },
              "blocks": {
                "heading": {
                  "type": "text",
                  "settings": {
                    "text": "<h2>Intentional design</h2>",
                    "type_preset": "h3",
                    "font": "var(--font-body--family)",
                    "font_size": "",
                    "line_height": "normal",
                    "letter_spacing": "normal",
                    "case": "none",
                    "wrap": "pretty",
                    "width": "100%",
                    "max_width": "normal",
                    "alignment": "center",
                    "padding-block-start": 0,
                    "padding-block-end": 0
                  }
                },
                "text": {
                  "type": "text",
                  "settings": {
                    "text": "<p>Everything we do starts with why</p>",
                    "type_preset": "rte",
                    "font": "var(--font-body--family)",
                    "font_size": "",
                    "line_height": "normal",
                    "letter_spacing": "normal",
                    "case": "none",
                    "wrap": "pretty",
                    "width": "100%",
                    "max_width": "normal",
                    "alignment": "center",
                    "padding-block-start": 0,
                    "padding-block-end": 0
                  }
                }
              },
              "block_order": ["heading", "text"]
            }
          },
          "block_order": ["icon", "group"]
        },
        "group_2": {
          "type": "group",
          "settings": {
            "content_direction": "column",
            "vertical_on_mobile": true,
            "horizontal_alignment": "flex-start",
            "vertical_alignment": "center",
            "horizontal_alignment_flex_direction_column": "center",
            "vertical_alignment_flex_direction_column": "center",
            "gap": 16,
            "height": "fit",
            "inherit_color_scheme": true
          },
          "blocks": {
            "icon": {
              "type": "icon",
              "settings": {
                "icon": "heart",
                "width": 32,
                "link": ""
              }
            },
            "group": {
              "type": "group",
              "settings": {
                "gap": 4
              },
              "blocks": {
                "heading": {
                  "type": "text",
                  "settings": {
                    "text": "<h2>Made with care</h2>",
                    "type_preset": "h3",
                    "font": "var(--font-body--family)",
                    "font_size": "",
                    "line_height": "normal",
                    "letter_spacing": "normal",
                    "case": "none",
                    "wrap": "pretty",
                    "width": "100%",
                    "max_width": "normal",
                    "alignment": "center",
                    "padding-block-start": 0,
                    "padding-block-end": 0
                  }
                },
                "text": {
                  "type": "text",
                  "settings": {
                    "text": "<p>We believe in building better</p>",
                    "type_preset": "rte",
                    "font": "var(--font-body--family)",
                    "font_size": "",
                    "line_height": "normal",
                    "letter_spacing": "normal",
                    "case": "none",
                    "wrap": "pretty",
                    "width": "100%",
                    "max_width": "normal",
                    "alignment": "center",
                    "padding-block-start": 0,
                    "padding-block-end": 0
                  }
                }
              },
              "block_order": ["heading", "text"]
            }
          },
          "block_order": ["icon", "group"]
        },
        "group_3": {
          "type": "group",
          "settings": {
            "content_direction": "column",
            "vertical_on_mobile": true,
            "horizontal_alignment": "flex-start",
            "vertical_alignment": "center",
            "horizontal_alignment_flex_direction_column": "center",
            "vertical_alignment_flex_direction_column": "center",
            "gap": 16,
            "height": "fit",
            "inherit_color_scheme": true
          },
          "blocks": {
            "icon": {
              "type": "icon",
              "settings": {
                "icon": "silhouette",
                "width": 32,
                "link": ""
              }
            },
            "group": {
              "type": "group",
              "settings": {
                "gap": 4
              },
              "blocks": {
                "heading": {
                  "type": "text",
                  "settings": {
                    "text": "<h2>A team with a goal</h2>",
                    "type_preset": "h3",
                    "font": "var(--font-body--family)",
                    "font_size": "",
                    "line_height": "normal",
                    "letter_spacing": "normal",
                    "case": "none",
                    "wrap": "pretty",
                    "width": "100%",
                    "max_width": "normal",
                    "alignment": "center",
                    "padding-block-start": 0,
                    "padding-block-end": 0
                  }
                },
                "text": {
                  "type": "text",
                  "settings": {
                    "text": "<p>Real people making great products</p>",
                    "type_preset": "rte",
                    "font": "var(--font-body--family)",
                    "font_size": "",
                    "line_height": "normal",
                    "letter_spacing": "normal",
                    "case": "none",
                    "wrap": "pretty",
                    "width": "100%",
                    "max_width": "normal",
                    "alignment": "center",
                    "padding-block-start": 0,
                    "padding-block-end": 0
                  }
                }
              },
              "block_order": ["heading", "text"]
            }
          },
          "block_order": ["icon", "group"]
        }
      },
      "block_order": ["group_1", "group_2", "group_3"],
      "settings": {
        "content_direction": "row",
        "vertical_on_mobile": true,
        "gap": 16,
        "section_width": "page-width",
        "padding-block-start": 48,
        "padding-block-end": 48
      }
    },
    {
      "name": "t:names.split_showcase",
      "category": "t:categories.banners",
      "blocks": {
        "group_1": {
          "type": "group",
          "settings": {
            "horizontal_alignment_flex_direction_column": "center",
            "vertical_alignment_flex_direction_column": "space-between",
            "gap": 32,
            "inherit_color_scheme": false,
            "color_scheme": "scheme-5",
            "background_media": "image",
            "height": "fill",
            "padding-block-start": 40,
            "padding-block-end": 40,
            "padding-inline-start": 40,
            "padding-inline-end": 40
          },
          "blocks": {
            "spacer": {
              "type": "spacer",
              "settings": {
                "size": "pixel",
                "pixel_size": 16
              }
            },
            "text_1": {
              "type": "text",
              "settings": {
                "text": "<h3>New arrivals</h3>",
                "type_preset": "h3",
                "width": "fit-content",
                "alignment": "center"
              }
            },
            "button": {
              "type": "button",
              "settings": {
                "label": "Shop now",
                "link": "shopify://collections/all",
                "style_class": "link"
              }
            }
          },
          "block_order": ["spacer", "text_1", "button"]
        },
        "group_2": {
          "type": "group",
          "settings": {
            "horizontal_alignment_flex_direction_column": "center",
            "vertical_alignment_flex_direction_column": "space-between",
            "gap": 32,
            "inherit_color_scheme": false,
            "color_scheme": "scheme-5",
            "background_media": "image",
            "height": "fill",
            "padding-block-start": 40,
            "padding-block-end": 40,
            "padding-inline-start": 40,
            "padding-inline-end": 40
          },
          "blocks": {
            "spacer": {
              "type": "spacer",
              "settings": {
                "size": "pixel",
                "pixel_size": 16
              }
            },
            "text_1": {
              "type": "text",
              "settings": {
                "text": "<h3>Bestsellers</h3>",
                "type_preset": "h3",
                "width": "fit-content",
                "alignment": "center"
              }
            },
            "button": {
              "type": "button",
              "settings": {
                "label": "Shop now",
                "link": "shopify://collections/all",
                "style_class": "link"
              }
            }
          },
          "block_order": ["spacer", "text_1", "button"]
        }
      },
      "block_order": ["group_1", "group_2"],
      "settings": {
        "content_direction": "row",
        "vertical_on_mobile": true,
        "gap": 0,
        "section_width": "full-width",
        "section_height": "large"
      }
    },
    {
      "name": "t:names.image_with_text",
      "category": "t:categories.storytelling",
      "settings": {
        "content_direction": "row",
        "gap": 32,
        "padding-block-start": 40,
        "padding-block-end": 40
      },
      "blocks": {
        "image": {
          "type": "image",
          "settings": {
            "image": "",
            "image_ratio": "square"
          }
        },
        "group": {
          "type": "group",
          "settings": {
            "horizontal_alignment_flex_direction_column": "flex-start",
            "width": "custom"
          },
          "blocks": {
            "heading": {
              "type": "text",
              "name": "t:names.heading",
              "settings": {
                "text": "<h2>Our signature product.</h2>",
                "type_preset": "h3"
              }
            },
            "text": {
              "type": "text",
              "settings": {
                "text": "<p>Made with care and unconditionally loved by our customers, this signature bestseller exceeds all expectations.</p>",
                "max_width": "narrow"
              }
            },
            "button": {
              "type": "button",
              "settings": {
                "label": "Shop now",
                "link": "shopify://collections/all"
              }
            }
          },
          "block_order": ["heading", "text", "button"]
        }
      },
      "block_order": ["image", "group"]
    },
    {
      "name": "t:names.large_logo",
      "category": "t:categories.banners",
      "blocks": {
        "text": {
          "type": "text",
          "settings": {
            "text": "<p>Made with care and unconditionally loved by our customers, this signature bestseller exceeds all expectations.</p>",
            "max_width": "narrow"
          }
        },
        "logo": {
          "type": "logo",
          "settings": {
            "unit": "percent",
            "percent_width": 100
          }
        }
      },
      "block_order": ["text", "logo"],
      "settings": {
        "vertical_alignment_flex_direction_column": "space-between",
        "gap": 16,
        "section_height": "medium",
        "color_scheme": "scheme-3",
        "padding-block-start": 40,
        "padding-block-end": 40
      }
    }
  ]
}
{% endschema %}
