{% # import schema from '../schemas/blocks/_footer-policy-list.js' %}

<ul
  class="
    policy_list list-unstyled
    custom-typography
    {% if block.settings.font_size != "" %}custom-font-size{% endif %}
  "
  style="{% render 'typography-style', preset: 'custom', settings: block.settings %}"
  {{ block.shopify_attributes }}
>
  {%- for policy in shop.policies -%}
    {%- if policy != blank -%}
      <li>
        <a
          href="{{ policy.url }}"
          class="footer-utilities__text"
        >
          {{- policy.title | escape -}}
        </a>
      </li>
    {%- endif -%}
  {%- endfor -%}
</ul>

{% stylesheet %}
  .policy_list {
    display: flex;
    gap: var(--gap-2xs) var(--gap-sm);
    /* Depending on the content, some smaller desktop layouts will need to wrap the items to fit everything */
    flex-wrap: wrap;
    justify-content: center;

    @media screen and (min-width: 750px) {
      justify-content: flex-start;
    }
  }
{% endstylesheet %}

{% schema %}
{
  "name": "t:names.policy_list",
  "tag": null,
  "settings": [
    {
      "type": "paragraph",
      "content": "t:content.manage_policies"
    },
    {
      "type": "select",
      "id": "font_size",
      "label": "t:settings.size",
      "options": [
        {
          "value": "0.625rem",
          "label": "10px"
        },
        {
          "value": "0.75rem",
          "label": "12px"
        },
        {
          "value": "0.875rem",
          "label": "14px"
        },
        {
          "value": "1rem",
          "label": "16px"
        },
        {
          "value": "1.125rem",
          "label": "18px"
        }
      ],
      "default": "0.75rem"
    },
    {
      "type": "select",
      "id": "case",
      "label": "t:settings.case",
      "options": [
        {
          "value": "none",
          "label": "t:options.default"
        },
        {
          "value": "uppercase",
          "label": "t:options.uppercase"
        }
      ],
      "default": "none"
    }
  ]
}
{% endschema %}
