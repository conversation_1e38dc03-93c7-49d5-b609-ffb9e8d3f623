{% # import schema from '../schemas/blocks/image' %}

{% liquid
  assign ratio = 1

  case block.settings.image_ratio
    when 'landscape'
      assign ratio = '16 / 9'
    when 'portrait'
      assign ratio = '4 / 5'
    when 'adapt'
      assign ratio = block.settings.image.aspect_ratio
  endcase

  if ratio == 0 or ratio == null
    assign ratio = 1
  endif
%}

{% capture class %}
  image-block image-block--{{ block.id }} image-block--height-{{ block.settings.height }} spacing-style size-style
{% endcapture %}

{% capture style %}
  --ratio: {{ ratio }};
  {% render 'size-style', settings: block.settings %}
  {% render 'spacing-style', settings: block.settings %}
{% endcapture %}

{% capture image_border_style %}
  --ratio: {{ ratio }};
  {% render 'border-override', settings: block.settings %}
{% endcapture %}

{% liquid
  assign media_width_desktop = '100vw'
  assign media_width_mobile = '100vw'
  assign sizes = 'auto, (min-width: 750px) ' | append: media_width_desktop | append: ', ' | append: media_width_mobile
  assign widths = '300, 450, 600, 750, 900, 1050, 1200, 1350, 1500, 1650, 1800, 1950, 2000, 2500, 3000, 3500, 4000, 5000'
%}

{% capture image %}
  {%- if block.settings.image -%}
    {{
      block.settings.image
      | image_url: width: 3840
      | image_tag:
        width: block.settings.image.width,
        widths: widths,
        height: block.settings.image.height,
        class: 'image-block__image border-style',
        style: image_border_style,
        sizes: sizes
    }}
  {%- else -%}
    <div class="placeholder-image border-style size-style" style="{{ image_border_style }}">
      <placeholder-image
        data-block-id="{{ block.id }}"
        data-type="general"
      ></placeholder-image>
    </div>
  {%- endif -%}
{% endcapture %}

{% if block.settings.link == blank %}
  <div
    class="{{ class }}"
    style="{{ style }}"
    {{ block.shopify_attributes }}
  >
    {{ image }}
  </div>
{% else %}
  <a
    href="{{ block.settings.link }}"
    class="{{ class }}"
    style="{{ style }}"
    {{ block.shopify_attributes }}
  >
    {{ image }}
  </a>
{% endif %}

{% stylesheet %}
  .placeholder-image {
    position: relative;
    aspect-ratio: var(--ratio);
    overflow: hidden;
  }

  placeholder-image img {
    width: 100%;
    height: 100%;
    aspect-ratio: var(--ratio);
  }

  .image-block {
    display: flex;
    /* When the image is nested in a group, section, etc, respect the parent's horizontal alignment */
    justify-content: var(--horizontal-alignment, 'inline-start');
  }

  .image-block--height-fill .image-block__image {
    height: 100%;
  }

  .image-block__image {
    object-fit: cover;
    aspect-ratio: var(--ratio);
  }
{% endstylesheet %}

{% schema %}
{
  "name": "t:names.image",
  "tag": null,
  "settings": [
    {
      "type": "image_picker",
      "id": "image",
      "label": "t:settings.image"
    },
    {
      "type": "url",
      "id": "link",
      "label": "t:settings.link"
    },
    {
      "type": "header",
      "content": "t:content.size"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "t:options.auto"
        },
        {
          "value": "portrait",
          "label": "t:options.portrait"
        },
        {
          "value": "square",
          "label": "t:options.square"
        },
        {
          "value": "landscape",
          "label": "t:options.landscape"
        }
      ],
      "default": "adapt",
      "label": "t:settings.aspect_ratio"
    },
    {
      "type": "select",
      "id": "width",
      "label": "t:settings.width_desktop",
      "options": [
        {
          "value": "fit-content",
          "label": "t:options.fit_content"
        },
        {
          "value": "fill",
          "label": "t:options.fill"
        },
        {
          "value": "custom",
          "label": "t:options.custom"
        }
      ],
      "default": "fill"
    },
    {
      "type": "range",
      "id": "custom_width",
      "label": "t:settings.custom_width",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "%",
      "default": 100,
      "visible_if": "{{ block.settings.width == 'custom' }}"
    },
    {
      "type": "select",
      "id": "width_mobile",
      "label": "t:settings.width_mobile",
      "options": [
        {
          "value": "fit-content",
          "label": "t:options.fit_content"
        },
        {
          "value": "fill",
          "label": "t:options.fill"
        },
        {
          "value": "custom",
          "label": "t:options.custom"
        }
      ],
      "default": "fill"
    },
    {
      "type": "range",
      "id": "custom_width_mobile",
      "label": "t:settings.custom_width",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "%",
      "default": 100,
      "visible_if": "{{ block.settings.width_mobile == 'custom' }}"
    },
    {
      "type": "select",
      "id": "height",
      "label": "t:settings.height",
      "options": [
        {
          "value": "fit",
          "label": "t:options.fit_content"
        },
        {
          "value": "fill",
          "label": "t:options.fill"
        }
      ],
      "default": "fit",
      "visible_if": "{{ block.settings.image_ratio == 'adapt' }}"
    },
    {
      "type": "header",
      "content": "t:content.borders"
    },
    {
      "type": "select",
      "id": "border",
      "label": "t:settings.style",
      "options": [
        {
          "value": "none",
          "label": "t:options.none"
        },
        {
          "value": "solid",
          "label": "t:options.solid"
        }
      ],
      "default": "none"
    },
    {
      "type": "range",
      "id": "border_width",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "t:settings.thickness",
      "default": 1,
      "visible_if": "{{ block.settings.border != 'none' }}"
    },
    {
      "type": "range",
      "id": "border_opacity",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "%",
      "label": "t:settings.opacity",
      "default": 100,
      "visible_if": "{{ block.settings.border != 'none' }}"
    },
    {
      "type": "range",
      "id": "border_radius",
      "label": "t:settings.border_radius",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "header",
      "content": "t:content.padding"
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "t:settings.top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "t:settings.bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-inline-start",
      "label": "t:settings.left",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-inline-end",
      "label": "t:settings.right",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "t:names.image",
      "category": "t:categories.basic"
    }
  ]
}
{% endschema %}
