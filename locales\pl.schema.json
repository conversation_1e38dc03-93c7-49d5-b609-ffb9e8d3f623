/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin language editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "names": {
    "404": "404",
    "borders": "Obramowania",
    "collapsible_row": "Zwijany wiersz",
    "custom_section": "Sekcja niestandardowa",
    "icon": "Ikona",
    "logo_and_favicon": "Logo i ikona Favicon",
    "product_buy_buttons": "Przyciski zakupu",
    "product_description": "Opis",
    "product_price": "Cena",
    "slideshow": "Po<PERSON>z slajdów",
    "typography": "Typografia",
    "video": "Film",
    "colors": "Kolory",
    "overlapping_blocks": "Nakładające się bloki",
    "product_variant_picker": "Selektor wariantów",
    "slideshow_controls": "Elementy sterujące pokazem slajdów",
    "size": "Rozmiar",
    "spacing": "Odstępy",
    "product_recommendations": "Polecane produkty",
    "product_media": "Multimedia produktu",
    "featured_collection": "Polecana kolekcja",
    "add_to_cart": "Dodaj do koszyka",
    "email_signup": "Rejestracja w celu otrzymywania e-maili",
    "submit_button": "Przycisk Prześlij",
    "grid_layout_selector": "Selektor układu siatki",
    "image": "Obraz",
    "list_items": "Pozycje listy",
    "facets": "Aspekty",
    "variants": "Warianty",
    "styles": "Style",
    "product_cards": "Karty produktów",
    "buttons": "Przyciski",
    "inputs": "Dane wejściowe",
    "primary_button": "Przycisk główny",
    "secondary_button": "Przycisk dodatkowy",
    "popovers": "Wyskakujące okienka",
    "marquee": "Baner",
    "alternating_content_rows": "Naprzemienne wiersze",
    "pull_quote": "Cytat typu pull",
    "contact_form": "Formularz kontaktowy",
    "featured_product": "Główne cechy produktu",
    "icons_with_text": "Ikony z tekstem",
    "product_list": "Lista produktów",
    "spacer": "Rozdzielacz",
    "products_carousel": "Lista produktów: karuzela",
    "products_grid": "Lista produktów: siatka",
    "accelerated_checkout": "Przyspieszona realizacja zakupu",
    "accordion": "Akordeon",
    "accordion_row": "Rząd akordeonu",
    "animations": "Animacje",
    "announcement": "Ogłoszenie",
    "announcement_bar": "Pasek ogłoszeń",
    "badges": "Znaczki",
    "button": "Przycisk",
    "cart": "Koszyk",
    "cart_items": "Pozycje w koszyku",
    "cart_products": "Produkty w koszyku",
    "cart_title": "Koszyk",
    "collection": "Kolekcja",
    "collection_card": "Karty kolekcji",
    "collection_columns": "Kolumny kolekcji",
    "collection_container": "Kolekcja",
    "collection_description": "Opisy kolekcji",
    "collection_image": "Obraz kolekcji",
    "collection_info": "Informacje o kolekcji",
    "collection_list": "Lista kolekcji",
    "collections": "Kolekcje",
    "content": "Treść",
    "content_grid": "Siatka treści",
    "details": "Szczegóły",
    "divider": "Separator",
    "filters": "Filtrowanie i sortowanie",
    "follow_on_shop": "Obserwuj w Shop",
    "footer": "Stopka",
    "footer_utilities": "Narzędzia stopki",
    "group": "Grupa",
    "header": "Nagłówek",
    "heading": "Nagłówek",
    "icons": "Ikony",
    "image_with_text": "Obraz z tekstem",
    "input": "Dane wejściowe",
    "logo": "Logo",
    "magazine_grid": "Siatka czasopisma",
    "media": "Multimedia",
    "menu": "Menu",
    "mobile_layout": "Układ na urządzeniu mobilnym",
    "payment_icons": "Ikony płatności",
    "popup_link": "Wyskakujący link",
    "predictive_search": "Wyskakujące okienko wyszukiwania",
    "predictive_search_empty": "Zapytania wyszukiwania z podpowiedziami są puste",
    "price": "Cena",
    "product": "Produkt",
    "product_card": "Karta produktów",
    "product_card_media": "Multimedia",
    "product_card_rendering": "Render karty produktów",
    "product_grid": "Siatka",
    "product_grid_main": "Siatka produktów",
    "product_image": "Obraz produktu",
    "product_information": "Informacje o produkcie",
    "product_review_stars": "Gwiazdki recenzji",
    "quantity": "Ilość",
    "row": "Wiersz",
    "search": "Wyszukiwanie",
    "section": "Sekcja",
    "selected_variants": "Wybrane warianty",
    "shop_the_look": "Kup stylizację",
    "slide": "Slajd",
    "social_media_links": "Linki do mediów społecznościowych",
    "steps": "Kroki",
    "summary": "Podsumowanie",
    "swatches": "Próbki",
    "testimonials": "Referencje",
    "text": "Tekst",
    "title": "Tytuł",
    "utilities": "Użyteczne funkcje",
    "search_input": "Wpis wyszukiwania",
    "search_results": "Wyniki wyszukiwania",
    "read_only": "Tylko do odczytu",
    "collection_title": "Tytuł kolekcji",
    "collections_bento": "Lista kolekcji: Bento",
    "faq_section": "Często zadawane pytanie",
    "hero": "Bohater",
    "jumbo_text": "Tekst jumbo",
    "view_all_button": "Wyświetl wszystko",
    "video_section": "Film",
    "custom_liquid": "Niestandardowy kod Liquid",
    "blog": "Blog",
    "blog_post": "Post na blogu",
    "blog_posts": "Posty na blogu",
    "caption": "Napis",
    "collection_card_image": "Obraz",
    "collection_links": "Linki do kolekcji",
    "collection_links_spotlight": "Linki kolekcji: Spotlight",
    "collection_links_text": "Linki fo kolekcji: tekst",
    "collections_carousel": "Lista kolekcji: karuzela",
    "collections_editorial": "Lista kolekcji: układ redakcyjny",
    "collections_grid": "Lista kolekcji: siatka",
    "copyright": "Prawo autorskie",
    "count": "Liczba",
    "divider_section": "Separator",
    "drawers": "Szuflady",
    "editorial": "Artykuł",
    "editorial_jumbo_text": "Artykuł: tekst jumbo",
    "hero_marquee": "Element główny: baner",
    "input_fields": "Pola wprowadzania",
    "local_pickup": "Odbiór lokalny",
    "marquee_section": "Baner",
    "media_with_text": "Media z tekstem",
    "page": "Strona",
    "page_content": "Treść",
    "page_layout": "Układ strony",
    "policy_list": "Linki do polityki",
    "prices": "Ceny",
    "products_editorial": "Lista produktów: układ redakcyjny",
    "social_link": "Link do mediów społecznościowych",
    "split_showcase": "Podzielona prezentacja",
    "variant_pickers": "Selektory wariantów",
    "product_title": "Tytuł produktu",
    "large_logo": "Duże logo",
    "product_list_button": "Przycisk Wyświetl wszystkie",
    "product_inventory": "Zapas produktu",
    "pills": "Przyciski",
    "description": "Opis"
  },
  "settings": {
    "autoplay": "Autoodtwarzanie",
    "background": "Tło",
    "border_radius": "Promień rogu",
    "border_width": "Grubość obramowania",
    "borders": "Obramowania",
    "bottom_padding": "Wypełnienie na dole",
    "color": "Kolor",
    "content_direction": "Kierunek zawartości",
    "content_position": "Położenie zawartości",
    "cover_image_size": "Wielkość obrazu w tle",
    "cover_image": "Obraz w tle",
    "custom_width": "Niestandardowa szerokość",
    "enable_video_looping": "Zapętlanie wideo",
    "favicon": "Ikona Favicon",
    "heading": "Nagłówek",
    "icon": "Ikona",
    "image_icon": "Ikona obrazu",
    "make_section_full_width": "Rozwiń sekcję na całą szerokość",
    "overlay_opacity": "Nieprzezroczystość nakładki",
    "padding": "Wypełnienie",
    "product": "Produkt",
    "text": "Tekst",
    "top_padding": "Wypełnienie na górze",
    "video": "Film",
    "video_alt_text": "Alternatywny tekst",
    "video_loop": "Zapętlony film",
    "video_position": "Pozycja filmu",
    "width": "Szerokość",
    "alignment": "Wyrównanie",
    "button": "Przycisk",
    "colors": "Kolory",
    "content_alignment": "Wyrównanie zawartości",
    "custom_minimum_height": "Niestandardowa minimalna wysokość",
    "font_family": "Rodzina czcionek",
    "gap": "Odstęp",
    "geometric_translate_y": "Przesunięcie geometryczne wzdłuż osi Y",
    "image": "Obraz",
    "image_opacity": "Nieprzezroczystość obrazu",
    "image_position": "Położenie obrazu",
    "image_ratio": "Proporcja obrazu",
    "label": "Etykieta",
    "line_height": "Wysokość linii",
    "link": "Link",
    "layout_gap": "Luka w układzie",
    "minimum_height": "Minimalna wysokość",
    "opacity": "Nieprzezroczystość",
    "primary_color": "Linki",
    "section_width": "Szerokość sekcji",
    "size": "Rozmiar",
    "slide_spacing": "Luka w slajdzie",
    "slide_width": "Szerokość slajdu",
    "slideshow_fullwidth": "Slajdy o pełnej szerokości",
    "style": "Styl",
    "text_case": "Wielkość liter",
    "z_index": "Indeks Z",
    "limit_content_width": "Ogranicz szerokość treści",
    "color_scheme": "Kolorystyka",
    "inherit_color_scheme": "Kolorystyka",
    "product_count": "Liczba produktów",
    "product_type": "Typ produktu",
    "content_width": "Szerokość treści",
    "collection": "Kolekcja",
    "enable_sticky_content": "Włącz przypiętą zawartość na komputerze",
    "error_color": "Błąd",
    "success_color": "Powodzenie",
    "primary_font": "Czcionka podstawowa",
    "secondary_font": "Czcionka dodatkowa",
    "tertiary_font": "Czcionka trzeciorzędna",
    "columns": "Kolumny",
    "items_to_show": "Pozycje do wyświetlenia",
    "layout": "Układ",
    "layout_type": "Typ",
    "show_grid_layout_selector": "Wyświetl selektor układu siatki",
    "view_more_show": "Pokaż przycisk „Wyświetl wszystkie”",
    "image_gap": "Odstęp między obrazami",
    "width_desktop": "Szerokość na komputerze",
    "width_mobile": "Szerokość na urządzeniu mobilnym",
    "border_style": "Styl obramowania",
    "height": "Wysokość",
    "thickness": "Grubość",
    "stroke": "Obrys",
    "filter_style": "Fitruj styl",
    "swatches": "Próbki",
    "quick_add_colors": "Szybkie dodawanie kolorów",
    "divider_color": "Separator",
    "border_opacity": "Nieprzezroczystość obramowania",
    "hover_background": "Tło obszaru aktywnego",
    "hover_borders": "Obramowanie obszaru aktywnego",
    "hover_text": "Tekst obszaru aktywnego",
    "primary_hover_color": "Linki obszaru aktywnego",
    "primary_button_text": "Tekst głównego przycisku",
    "primary_button_background": "Tło głównego przycisku",
    "primary_button_border": "Obramowanie przycisku głównego",
    "secondary_button_text": "Tekst przycisku dodatkowego",
    "secondary_button_background": "Tło przycisku dodatkowego",
    "secondary_button_border": "Obramowanie przycisku dodatkowego",
    "shadow_color": "Cień",
    "video_autoplay": "Autoodtwarzanie",
    "video_cover_image": "Obraz okładki",
    "video_external_url": "Adres URL",
    "video_source": "Źródło",
    "background_color": "Kolor tła",
    "first_row_media_position": "Pozycja multimediów w pierwszym wierszu",
    "hide_padding": "Ukryj wypełnienie",
    "size_mobile": "Rozmiar mobilny",
    "pixel_size_mobile": "Rozmiar w pikselach",
    "percent_size_mobile": "Rozmiar w procentach",
    "unit": "Jednostka",
    "custom_mobile_size": "Niestandardowy rozmiar mobilny",
    "fixed_height": "Wysokość w pikselach",
    "fixed_width": "Szerokość w pikselach",
    "percent_height": "Wysokość w procentach",
    "percent_width": "Szerokość w procentach",
    "percent_size": "Rozmiar w procentach",
    "pixel_size": "Rozmiar w pikselach",
    "card_image_height": "Wysokość obrazu produktu",
    "logo_font": "Czcionka logo",
    "accordion": "Akordeon",
    "aspect_ratio": "Współczynnik proporcji",
    "auto_rotate_announcements": "Automatyczna zmiana ogłoszeń",
    "auto_rotate_slides": "Automatyczna zmiana slajdów",
    "badge_corner_radius": "Promień rogu",
    "badge_position": "Położenie na kartach",
    "badge_sale_color_scheme": "W promocji",
    "badge_sold_out_color_scheme": "Wyprzedane",
    "behavior": "Zachowanie",
    "blur": "Rozmycie cienia",
    "border": "Obramowanie",
    "bottom": "Dół",
    "carousel_on_mobile": "Karuzela na urządzenia mobilne",
    "cart_count": "Liczba produktów w koszyku",
    "cart_items": "Pozycje w koszyku",
    "cart_related_products": "Powiązane produkty",
    "cart_title": "Koszyk",
    "cart_total": "Całkowita wartość koszyka",
    "cart_type": "Typ",
    "case": "Wielkość liter",
    "checkout_buttons": "Przyciski przyspieszonej realizacji zakupu",
    "collection_list": "Kolekcje",
    "collection_templates": "Szablony kolekcji",
    "content": "Treść",
    "corner_radius": "Promień rogu",
    "country_region": "Kraj/region",
    "currency_code": "Kod waluty",
    "custom_height": "Niestandardowa wysokość",
    "desktop_height": "Szerokość na komputerze",
    "direction": "Kierunek",
    "display": "Wyświetl",
    "divider_thickness": "Grubość separatora",
    "divider": "Separator",
    "dividers": "Separatory",
    "drop_shadow": "Rzuć cień",
    "empty_state_collection_info": "Wyświetlane przed wprowadzeniem wyszukiwania",
    "empty_state_collection": "Status pustej kolekcji",
    "enable_filtering": "Filtry",
    "enable_grid_density": "Kontrola układu siatki",
    "enable_sorting": "Sortowanie",
    "enable_zoom": "Włącz powiększenie",
    "equal_columns": "Równe kolumny",
    "expand_first_group": "Rozwiń pierwszą grupę",
    "extend_media_to_screen_edge": "Rozciągnij multimedia do krawędzi ekranu",
    "extend_summary": "Rozciągnij do krawędzi ekranu",
    "extra_large": "Bardzo duży",
    "extra_small": "Bardzo mała",
    "flag": "Flaga",
    "font_price": "Czcionka ceny",
    "font_weight": "Grubość czcionki",
    "font": "Czcionka",
    "full_width_first_image": "Pierwszy obraz o pełnej szerokości",
    "full_width_on_mobile": "Pełna szerokość na urządzeniu mobilnym",
    "heading_preset": "Ustawienie wstępne nagłówka",
    "hide_unselected_variant_media": "Ukryj niewybrane pliki multimedialne wariantów",
    "horizontal_gap": "Odstęp w poziomie",
    "horizontal_offset": "Poziome przesunięcie cienia",
    "hover_behavior": "Zachowanie po najechaniu kursorem",
    "icon_background": "Tło ikony",
    "icons": "Ikony",
    "image_border_radius": "Promień rogu obrazu",
    "installments": "Raty",
    "integrated_button": "Zintegrowany przycisk",
    "language_selector": "Selektor języka",
    "large": "Duży",
    "left_padding": "Wypełnienie z lewej",
    "left": "Lewa strona",
    "letter_spacing": "Odstęp między literami",
    "limit_media_to_screen_height": "Ograniczenie do wysokości ekranu",
    "limit_product_details_width": "Ogranicz szerokość szczegółów produktu",
    "link_preset": "Ustawienie wstępne linku",
    "links": "Linki",
    "logo": "Logo",
    "loop": "Pętla",
    "make_details_sticky_desktop": "Przyklejony na komputerach",
    "max_width": "Maks. szerokość",
    "media_height": "Wysokość multimediów",
    "media_overlay": "Nakładka na multimediach",
    "media_position": "Pozycja multimediów",
    "media_type": "Typ multimediów",
    "media_width": "Szerokość multimediów",
    "menu": "Menu",
    "mobile_columns": "Kolumny na urządzeniu mobilnym",
    "mobile_height": "Wysokość na urządzeniu mobilnym",
    "mobile_logo_image": "Logo na urządzeniu mobilnym",
    "mobile_quick_add": "Szybkie dodawanie na urządzeniu mobilnym",
    "motion_direction": "Kierunek ruchu",
    "motion": "Ruch",
    "movement_direction": "Kierunek ruchu",
    "navigation_bar_color_scheme": "Schemat kolorów paska nawigacji",
    "navigation_bar": "Pasek nawigacji",
    "navigation": "Nawigacja",
    "open_new_tab": "Otwórz link w nowej karcie",
    "overlay_color": "Kolor nakładki",
    "overlay": "Nakładka",
    "padding_bottom": "Wypełnienie na dole",
    "padding_horizontal": "Wypełnienie w poziomie",
    "padding_top": "Wypełnienie na górze",
    "page_width": "Szerokość strony",
    "pagination": "Paginacja",
    "placement": "Umieszczanie",
    "position": "Pozycja",
    "preset": "Ustawienie wstępne",
    "product_cards": "Karty produktów",
    "product_pages": "Strony produktu",
    "product_templates": "Szablony produktów",
    "products": "Produkty",
    "quick_add": "Szybkie dodawanie",
    "ratio": "Proporcja",
    "regular": "Zwykły",
    "review_count": "Liczba recenzji",
    "right": "Prawa strona",
    "row_height": "Wysokość rzędu",
    "row": "Wiersz",
    "seller_note": "Zezwalaj na dodawanie uwag dla sprzedawcy",
    "shape": "Kształt",
    "show_as_accordion": "Wyświetlaj jako akordeon na urządzeniach mobilnych",
    "show_sale_price_first": "Najpierw pokaż cenę promocyjną",
    "show_tax_info": "Informacje podatkowe",
    "show": "Pokaż",
    "small": "Mała",
    "speed": "Szybkość",
    "statement": "Wyciąg",
    "sticky_header": "Przyklejony nagłówek",
    "text_hierarchy": "Hierarchia tekstu",
    "text_presets": "Ustawienia wstępne tekstu",
    "title": "Tytuł",
    "top": "Góra",
    "type": "Typ",
    "type_preset": "Ustawienie wstępne tekstu",
    "underline_thickness": "Grubość podkreślenia",
    "variant_images": "Obrazy wariantów",
    "vendor": "Dostawca",
    "vertical_gap": "Odstęp w pionie",
    "vertical_offset": "Przesunięcie cienia w pionie",
    "vertical_on_mobile": "Pionowo na urządzeniu mobilnym",
    "view_all_as_last_card": "„Wyświetl wszystkie” jako ostatnia karta",
    "weight": "Waga",
    "wrap": "Zawijaj",
    "read_only": "Tylko do odczytu",
    "always_stack_buttons": "Zawsze układaj przyciski",
    "custom_mobile_width": "Niestandardowa szerokość na urządzeniu mobilnym",
    "gradient_direction": "Kierunek gradientu",
    "overlay_style": "Styl nakładki",
    "shadow_opacity": "Krycie cienia",
    "show_filter_label": "Etykiety tekstowe dla zastosowanych filtrów",
    "show_swatch_label": "Etykiety tekstowe dla próbek",
    "transparent_background": "Przezroczyste tło",
    "account": "Konto",
    "align_baseline": "Wyrównaj linię bazową tekstu",
    "add_discount_code": "Włącz rabaty w koszyku",
    "background_overlay": "Nakładka tła",
    "background_media": "Multimedia w tle",
    "border_thickness": "Grubość obramowania",
    "bottom_row": "Wiersz dolny",
    "button_text_case": "Wielkość liter tekstu",
    "button_text_weight": "Waga tekstu",
    "auto_open_cart_drawer": "„Dodaj do koszyka” automatycznie otwiera szufladę",
    "collection_count": "Liczba kolekcji",
    "custom_liquid": "Kod Liquid",
    "default": "Domyślne",
    "default_logo": "Domyślne logo",
    "divider_width": "Szerokość separatora",
    "headings": "Nagłówki",
    "hide_logo_on_home_page": "Ukryj logo na stronie głównej",
    "horizontal_padding": "Wypełnienie poziome",
    "inverse": "Odwrócenie",
    "inverse_logo": "Odwrócone logo",
    "layout_style": "Styl",
    "length": "Długość",
    "mobile_pagination": "Paginacja mobilna",
    "open_row_by_default": "Domyślnie otwórz wiersz",
    "page_transition_enabled": "Przejścia między stronami",
    "search": "Wyszukiwanie",
    "search_icon": "Ikona wyszukiwania",
    "search_position": "Pozycja",
    "search_row": "Wiersz",
    "show_author": "Autor",
    "show_alignment": "Pokaż dopasowanie",
    "show_count": "Pokaż liczbę",
    "show_date": "Data",
    "show_pickup_availability": "Pokaż możliwość odbioru",
    "show_search": "Pokaż wyszukiwanie",
    "use_inverse_logo": "Użyj odwróconego logo",
    "vertical_padding": "Wypełnienie pionowe",
    "visibility": "Widoczność",
    "product_corner_radius": "Promień rogu produktu",
    "card_corner_radius": "Promień rogu karty",
    "alignment_mobile": "Dopasowanie na urządzeniu mobilnym",
    "animation_repeat": "Powtórz animację",
    "blurred_reflection": "Zamazane odbicie",
    "card_hover_effect": "Efekt najechania kursorem na kartę",
    "card_size": "Rozmiar karty",
    "collection_title_case": "Wielkość liter tytułu kolekcji",
    "effects": "Efekty",
    "inventory_threshold": "Niski próg zapasu",
    "mobile_card_size": "Rozmiar karty mobilnej",
    "page": "Strona",
    "product_and_card_title_case": "Wielkość liter tytułu produktu i karty",
    "product_title_case": "Wielkość liter tytułu produktu",
    "reflection_opacity": "Nieprzezroczystość odbicia",
    "right_padding": "Wypełnienie z prawej",
    "show_inventory_quantity": "Wyświetl niski poziom zapasu",
    "text_label_case": "Wielkość liter etykiety tekstowej",
    "transition_to_main_product": "Przejście z karty produktu do strony produktu",
    "show_second_image_on_hover": "Pokaż drugi obraz po najechaniu kursorem",
    "media": "Multimedia",
    "product_card_carousel": "Wyświetl karuzelę"
  },
  "options": {
    "adapt_to_image": "Dostosuj do obrazu",
    "apple": "Jabłko",
    "arrow": "Strzałka",
    "banana": "Banan",
    "bottle": "Butelka",
    "box": "Skrzynka",
    "buttons": "Przyciski",
    "carrot": "Marchewka",
    "center": "Środek",
    "chat_bubble": "Dymek czatu",
    "clipboard": "Podkładka do pisania",
    "contain": "Zawiera",
    "counter": "Licznik",
    "cover": "Strona tytułowa",
    "custom": "Niestandardowy",
    "dairy_free": "Bezmleczne",
    "dairy": "Nabiał",
    "dropdowns": "Listy rozwijane",
    "dots": "Kropki",
    "dryer": "Suszarka",
    "end": "Koniec",
    "eye": "Oko",
    "facebook": "Facebook",
    "fire": "Ogień",
    "gluten_free": "Bez glutenu",
    "heart": "Serce",
    "horizontal": "W poziomie",
    "instagram": "Instagram",
    "iron": "Żelazko",
    "large": "Duży",
    "leaf": "Liść",
    "leather": "Skóra",
    "lightning_bolt": "Błyskawica",
    "lipstick": "Pomadka do ust",
    "lock": "Zamek",
    "map_pin": "Pinezka na mapie",
    "medium": "Średni",
    "none": "Brak",
    "numbers": "Liczby",
    "nut_free": "Bez orzechów",
    "pants": "Spodnie",
    "paw_print": "Odcisk łapy",
    "pepper": "Pieprz",
    "perfume": "Perfumy",
    "pinterest": "Pinterest",
    "plane": "Samolot",
    "plant": "Roślina",
    "price_tag": "Metka z ceną",
    "question_mark": "Znak zapytania",
    "recycle": "Zutylizuj",
    "return": "Zwrot",
    "ruler": "Linijka",
    "serving_dish": "Naczynie do serwowania",
    "shirt": "Koszula",
    "shoe": "But",
    "silhouette": "Sylwetka",
    "small": "Mały",
    "snapchat": "Snapchat",
    "snowflake": "Płatek śniegu",
    "star": "Gwiazdka",
    "start": "Początek",
    "stopwatch": "Stoper",
    "tiktok": "TikTok",
    "truck": "Ciężarówka",
    "tumblr": "Tumblr",
    "twitter": "X (Twitter)",
    "vertical": "W pionie",
    "vimeo": "Vimeo",
    "washing": "Pranie",
    "auto": "Automatyczny",
    "default": "Domyślny",
    "fill": "Wypełnienie",
    "fit": "Dopasowanie",
    "full": "Pełne",
    "full_and_page": "Pełne tło, zawartość na szerokość strony",
    "heading": "Nagłówek",
    "landscape": "Poziomo",
    "lg": "LG",
    "link": "Link",
    "lowercase": "małe litery",
    "m": "M",
    "outline": "Obrys",
    "page": "Strona",
    "portrait": "Pionowo",
    "s": "S",
    "sentence": "Zdanie",
    "solid": "Pełny",
    "space_between": "Odstęp między",
    "square": "Kwadratowy",
    "uppercase": "Duże litery",
    "circle": "Koło",
    "swatches": "Próbki",
    "full_and_page_offset_left": "Pełne tło, treść na szerokość strony, przesunięcie w lewo",
    "full_and_page_offset_right": "Pełne tło, treść na szerokość strony, przesunięcie w prawo",
    "offset_left": "Przesunięcie w lewo",
    "offset_right": "Przesunięcie w prawo",
    "page_center_aligned": "Strona z wyrównaniem do środka",
    "page_left_aligned": "Strona wyrównana do lewej",
    "page_right_aligned": "Strona wyrównana do prawej",
    "button": "Przycisk",
    "caption": "Napis",
    "h1": "Nagłówek 1",
    "h2": "Nagłówek 2",
    "h3": "Nagłówek 3",
    "h4": "Nagłówek 4",
    "h5": "Nagłówek 5",
    "h6": "Nagłówek 6",
    "paragraph": "Akapit",
    "primary": "Główna",
    "secondary": "Dodatkowa",
    "tertiary": "Trzeciorzędna",
    "chevron_left": "Pagon skierowany w lewo",
    "chevron_right": "Pagon skierowany w prawo",
    "diamond": "Romb",
    "grid": "Siatka",
    "parallelogram": "Równoległobok",
    "rounded": "Zaokrąglenie",
    "fit_content": "Dopasowanie",
    "pills": "Przyciski",
    "heavy": "Gruby",
    "thin": "Cienki",
    "drawer": "Szuflada",
    "preview": "Podgląd",
    "text": "Tekst",
    "video_uploaded": "Przesłany",
    "video_external_url": "Zewnętrzny adres URL",
    "up": "W górę",
    "down": "W dół",
    "gradient": "Gradient",
    "fixed": "Stałe",
    "pixel": "Piksel",
    "percent": "Procent",
    "aspect_ratio": "Współczynnik proporcji",
    "above_carousel": "Powyżej karuzeli",
    "all": "Wszystkie",
    "always": "Zawsze",
    "arrows_large": "Duże strzałki",
    "arrows": "Strzałki",
    "balance": "Balance",
    "bento": "Bento",
    "black": "Czarny",
    "bluesky": "Bluesky",
    "body_large": "Tekst podstawowy (duży)",
    "body_regular": "Tekst podstawowy (standardowy)",
    "body_small": "Tekst podstawowy (mały)",
    "bold": "Pogrubienie",
    "bottom_left": "Lewy dolny",
    "bottom_right": "Prawy dolny",
    "bottom": "Dół",
    "capitalize": "Wielkie litery",
    "caret": "Trójkątna strzałka",
    "carousel": "Karuzela",
    "check_box": "Pole wyboru",
    "chevron_large": "Duża jodełka",
    "chevron": "Jodełka",
    "chevrons": "Jodełka",
    "classic": "Klasyczny",
    "collection_images": "Obraz kolekcji",
    "color": "Kolor",
    "complementary": "Uzupełniające",
    "dissolve": "Rozmyj",
    "dotted": "Kropkowane",
    "editorial": "Redakcyjny",
    "extra_large": "Bardzo duży",
    "extra_small": "Bardzo mała",
    "featured_collections": "Polecane kolekcje",
    "featured_products": "Polecane produkty",
    "font_primary": "Główna",
    "font_secondary": "Dodatkowy",
    "font_tertiary": "Trzeciorzędna",
    "forward": "Dalej",
    "full_screen": "Tryb pełnoekranowy",
    "heading_extra_large": "Nagłówek (bardzo duży)",
    "heading_extra_small": "Nagłówek (bardzo mały)",
    "heading_large": "Nagłówek (duży)",
    "heading_regular": "Nagłówek (standardowy)",
    "heading_small": "Nagłówek (mały)",
    "icon": "Ikona",
    "image": "Obraz",
    "input": "Dane wejściowe",
    "inside_carousel": "Wewnątrz karuzeli",
    "inverse_large": "Odwrócone duże",
    "inverse": "Odwrócone",
    "large_arrows": "Duże strzałki",
    "large_chevrons": "Duża jodełka",
    "left": "Lewa strona",
    "light": "Jasny",
    "linkedin": "LinkedIn",
    "loose": "Luźny",
    "media_first": "Multimedia w pierwszej kolejności",
    "media_second": "Multimedia w drugiej kolejności",
    "modal": "Tryb modalny",
    "narrow": "Wąski",
    "never": "Nigdy",
    "next_to_carousel": "Obok karuzeli",
    "normal": "Standard",
    "nowrap": "Bez zawijania",
    "off_media": "Poza multimediami",
    "on_media": "Na multimediach",
    "on_scroll_up": "Przy przewijaniu w górę",
    "one_half": "1/2",
    "one_number": "1",
    "one_third": "1/3",
    "pill": "Okrągły przełącznik",
    "plus": "Plus",
    "pretty": "Pretty",
    "price": "Cena",
    "primary_style": "Styl podstawowy",
    "rectangle": "Prostokąt",
    "regular": "Zwykły",
    "related": "Powiązane",
    "reverse": "Odwróć",
    "rich_text": "Tekst sformatowany",
    "right": "Prawa strona",
    "secondary_style": "Styl dodatkowy",
    "semibold": "Półpogrubiony",
    "shaded": "Cieniowany",
    "show_second_image": "Pokaż drugi obraz",
    "single": "Pojedyncze",
    "slide_left": "Przesuń w lewo",
    "slide_up": "Przesuń w górę",
    "spotify": "Spotify",
    "stack": "Stos",
    "text_only": "Tylko tekst",
    "threads": "Threads",
    "thumbnails": "Miniatury",
    "tight": "Tight",
    "top_left": "U góry po lewej",
    "top_right": "U góry po prawej",
    "top": "Góra",
    "two_number": "2",
    "two_thirds": "2/3",
    "underline": "Podkreślenie",
    "video": "Film",
    "wide": "Szeroki",
    "youtube": "Youtube",
    "accent": "Akcent",
    "below_image": "Pod obrazem",
    "body": "Tekst podstawowy",
    "button_primary": "Przycisk główny",
    "button_secondary": "Przycisk dodatkowy",
    "compact": "Kompaktowa",
    "crop_to_fit": "Przytnij, aby dopasować",
    "hidden": "Ukryte",
    "hint": "Wskazówka",
    "maintain_aspect_ratio": "Zachowaj współczynnik proporcji",
    "off": "Wył.",
    "on_image": "Na obrazie",
    "social_bluesky": "Media społecznościowe: Bluesky",
    "social_facebook": "Media społecznościowe: Facebook",
    "social_instagram": "Media społecznościowe: Instagram",
    "social_linkedin": "Media społecznościowe: LinkedIn",
    "social_pinterest": "Media społecznościowe: Pinterest",
    "social_snapchat": "Media społecznościowe: Snapchat",
    "social_spotify": "Media społecznościowe: Spotify",
    "social_threads": "Media społecznościowe: Threads",
    "social_tiktok": "Media społecznościowe: TikTok",
    "social_tumblr": "Media społecznościowe: Tumblr",
    "social_twitter": "Media społecznościowe: X (Twitter)",
    "social_whatsapp": "Media społecznościowe: WhatsApp",
    "social_vimeo": "Media społecznościowe: Vimeo",
    "social_youtube": "Media społecznościowe: YouTube",
    "spotlight": "Spotlight",
    "standard": "Standardowa",
    "subheading": "Nagłówek podrzędny",
    "blur": "Zamaż",
    "lift": "Uniesienie",
    "reveal": "Pokaż",
    "scale": "Skalowanie",
    "subtle_zoom": "Powiększenie"
  },
  "content": {
    "background_video": "Film w tle",
    "describe_the_video_for": "Opisz film dla klientów korzystających z czytników ekranu. [Dowiedz się więcej](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)",
    "width_is_automatically_optimized": "Szerokość jest automatycznie dostosowywana do urządzeń mobilnych.",
    "advanced": "Zaawansowane",
    "background_image": "Obraz tła",
    "block_size": "Rozmiar bloku",
    "borders": "Obramowania",
    "section_size": "Rozmiar sekcji",
    "slideshow_width": "Szerokość slajdu",
    "typography": "Typografia",
    "complementary_products": "Produkty uzupełniające należy skonfigurować za pomocą aplikacji Search & Discovery. [Dowiedz się więcej](https://help.shopify.com/manual/online-store/search-and-discovery)",
    "mobile_column_optimization": "Kolumny będą automatycznie optymalizowane pod kątem urządzeń mobilnych",
    "content_width": "Szerokość treści ma zastosowanie tylko wtedy, gdy szerokość sekcji jest ustawiona na pełny zakres.",
    "adjustments_affect_all_content": "Dotyczy całej zawartości w tym bloku",
    "responsive_font_sizes": "Rozmiary są automatycznie skalowane dla wszystkich rozmiarów ekranów",
    "buttons": "Przyciski",
    "swatches": "Próbki",
    "variant_settings": "Ustawienia wariantów",
    "background": "Tło",
    "cards_layout": "Układ kart",
    "section_layout": "Układ sekcji",
    "mobile_size": "Rozmiar mobilny",
    "appearance": "Wygląd",
    "arrows": "Strzałki",
    "body_size": "Rozmiar tekstu podstawowego",
    "bottom_row_appearance": "Wygląd dolnego rzędu",
    "carousel_navigation": "Nawigacja po karuzeli",
    "carousel_pagination": "Paginacja karuzeli",
    "copyright": "Prawo autorskie",
    "edit_logo_in_theme_settings": "Edytuj logo w [ustawieniach szablonu](/editor?context=theme&category=logo%20and%20favicon)",
    "edit_price_in_theme_settings": "Edytuj format cen w [ustawieniach szablonu](/editor?context=theme&category=currency%20code)",
    "edit_variants_in_theme_settings": "Edytuj styl wariantów w [ustawieniach szablonu](/editor?context=theme&category=variants)",
    "email_signups_create_customer_profiles": "Rejestracje dodają [profile klienta](https://help.shopify.com/manual/customers)",
    "follow_on_shop_eligiblity": "Aby przycisk był widoczny, kanał Shop musi być zainstalowany, a usługa Shop Pay musi być aktywna. [Dowiedz się więcej](https://help.shopify.com/en/manual/online-store/themes/customizing-themes/add-shop-buttons)",
    "fonts": "Czcionki",
    "grid": "Siatka",
    "heading_size": "Rozmiar nagłówka",
    "image": "Obraz",
    "input": "Dane wejściowe",
    "layout": "Układ",
    "link": "Link",
    "link_padding": "Wypełnienie linku",
    "localization": "Lokalizacja",
    "logo": "Logo",
    "margin": "Marża",
    "media": "Multimedia",
    "media_1": "Multimedia 1",
    "media_2": "Multimedia 2",
    "menu": "Menu",
    "mobile_layout": "Układ na urządzeniu mobilnym",
    "padding": "Wypełnienie",
    "padding_desktop": "Wypełnienie pulpitu",
    "paragraph": "Akapit",
    "policies": "Zasady",
    "popup": "Wyskakujące okienko",
    "search": "Wyszukiwanie",
    "size": "Rozmiar",
    "social_media": "Media społecznościowe",
    "submit_button": "Przycisk Prześlij",
    "text_presets": "Ustawienia wstępne tekstu",
    "transparent_background": "Przezroczyste tło",
    "typography_primary": "Typografia podstawowa",
    "typography_secondary": "Typografia dodatkowa",
    "typography_tertiary": "Typografia trzeciorzędna",
    "mobile_width": "Szerokość na urządzeniu mobilnym",
    "width": "Szerokość",
    "carousel": "Karuzela",
    "colors": "Kolory",
    "collection_page": "Strona kolekcji",
    "copyright_info": "Dowiedz się, jak [edytować Twoje oświadczenie o prawach autorskich](https://help.shopify.com/manual/online-store/themes/customizing-themes/remove-powered-by-shopify-message)",
    "customer_account": "Konto klienta",
    "edit_empty_state_collection_in_theme_settings": "Edytuj kolekcję pustych stanów w [ustawieniach szablonu](/editor?context=theme&category=search)",
    "home_page": "Strona główna",
    "images": "Zdjęcia",
    "inverse_logo_info": "Używana po ustawieniu transparentnego tła nagłówka na „Odwrócenie”",
    "manage_customer_accounts": "[Zarządzaj widocznością](/admin/settings/customer_accounts) w ustawieniach konta klienta. Starsze konta nie są obsługiwane.",
    "manage_policies": "[Zarządzaj politykami](/admin/settings/legal)",
    "product_page": "Strona produktu",
    "text": "Tekst",
    "thumbnails": "Miniatury",
    "visibility": "Widoczność",
    "visible_if_collection_has_more_products": "Widoczne, jeśli kolekcja ma więcej produktów niż pokazano",
    "grid_layout": "Układ siatki",
    "app_required_for_ratings": "Do oceniania produktów wymagana jest aplikacja. [Dowiedz się więcej](https://help.shopify.com/manual/apps)",
    "icon": "Ikona",
    "manage_store_name": "[Zarządzaj nazwą sklepu](/admin/settings/general?edit=storeName)",
    "resource_reference_collection_card": "Wyświetla kolekcję z sekcji nadrzędnej",
    "resource_reference_collection_card_image": "Wyświetla obraz z kolekcji nadrzędnej",
    "resource_reference_collection_title": "Wyświetla tytuł z kolekcji nadrzędnej",
    "resource_reference_product": "Automatycznie łączy z produktem nadrzędnym",
    "resource_reference_product_card": "Wyświetla produkt z sekcji nadrzędnej",
    "resource_reference_product_inventory": "Wyświetla zapas z produktu nadrzędnego",
    "resource_reference_product_price": "Wyświetla cenę z produktu nadrzędnego",
    "resource_reference_product_recommendations": "Wyświetla polecenia na podstawie produktu nadrzędnego",
    "resource_reference_product_review": "Wyświetla recenzje z produktu nadrzędnego",
    "resource_reference_product_swatches": "Wyświetla próbki z produktu nadrzędnego",
    "resource_reference_product_title": "Wyświetla tytuł z produktu nadrzędnego",
    "resource_reference_product_variant_picker": "Wyświetla warianty z produktu nadrzędnego",
    "resource_reference_product_media": "Wyświetla multimedia z produktu nadrzędnego"
  },
  "html_defaults": {
    "share_information_about_your": "<p>Udostępnij klientom informacje o swojej marce. Opisz produkt, udostępnij ogłoszenia lub przywitaj klientów w swoim sklepie.</p>"
  },
  "text_defaults": {
    "collapsible_row": "Zwijany wiersz",
    "button_label": "Kup teraz",
    "heading": "Nagłówek",
    "email_signup_button_label": "Subskrybuj",
    "accordion_heading": "Nagłówek akordeonu",
    "contact_form_button_label": "Prześlij",
    "popup_link": "Wyskakujący link",
    "sign_up": "Zarejestruj się",
    "welcome_to_our_store": "Witamy w naszym sklepie",
    "be_bold": "Odważ się.",
    "shop_our_latest_arrivals": "Odkryj nasze najnowsze produkty!"
  },
  "info": {
    "video_alt_text": "Opisz film dla użytkowników technologii asystujących",
    "video_autoplay": "Filmy będą domyślnie wyciszone",
    "video_external": "Użyj adresu URL do YouTube lub Vimeo",
    "link_info": "Opcjonalnie: umożliwia kliknięcie ikony",
    "carousel_layout_on_mobile": "Karuzela jest używana na urządzeniach mobilnych",
    "carousel_hover_behavior_not_supported": "Najechanie kursorem na karuzelę nie jest obsługiwane, gdy wybrano karuzelę na poziomie sekcji.",
    "checkout_buttons": "Umożliwia kupującym szybsze dokonanie zakupu i może poprawić konwersję. [Dowiedz się więcej](https://help.shopify.com/manual/online-store/dynamic-checkout)",
    "custom_heading": "Niestandardowy nagłówek",
    "edit_presets_in_theme_settings": "Edytuj ustawienia wstępne w [ustawieniach szablonu](/editor?context=theme&category=typography)",
    "enable_filtering_info": "Dostosuj filtry za pomocą [aplikacji Search & Discovery](https://help.shopify.com/manual/online-store/search-and-discovery/filters)",
    "grid_layout_on_mobile": "Układ siatki jest używany dla urządzeń mobilnych",
    "logo_font": "Ma zastosowanie tylko wtedy, gdy logo nie jest wybrane",
    "manage_countries_regions": "[Zarządzaj krajami/regionami](/admin/settings/markets)",
    "manage_languages": "[Zarządzaj językami](/admin/settings/languages)",
    "transparent_background": "Sprawdź każdy szablon, w którym zastosowano przezroczyste tło, pod kątem czytelności",
    "aspect_ratio_adjusted": "Dostosowano w niektórych układach",
    "auto_open_cart_drawer": "Gdy ta opcja jest aktywna, szuflada koszyka otworzy się automatycznie po dodaniu produktu do koszyka.",
    "custom_liquid": "Dodaj fragmenty kodu aplikacji lub inny kod, aby utworzyć zaawansowane dostosowania. [Dowiedz się więcej](https://shopify.dev/docs/api/liquid)",
    "applies_on_image_only": "Dotyczy tylko obrazów",
    "hover_effects": "Dotyczy kart produktów i kolekcji",
    "pills_usage": "Używane przez zastosowane filtry, kody rabatowe i sugestie wyszukiwania"
  },
  "categories": {
    "product_list": "Lista produktów",
    "basic": "Basic",
    "collection": "Kolekcja",
    "collection_list": "Lista kolekcji",
    "footer": "Stopka",
    "forms": "Formularze",
    "header": "Nagłówek",
    "layout": "Układ",
    "links": "Linki",
    "product": "Produkt",
    "banners": "Banery",
    "collections": "Kolekcje",
    "custom": "Niestandardowy",
    "decorative": "Ozdobne",
    "products": "Produkty",
    "other_sections": "Inny",
    "storytelling": "Opowiadanie historii"
  }
}
