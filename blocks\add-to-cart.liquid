{% # import schema from '../schemas/blocks/add-to-cart.js' %}

{%- doc -%}
  This block is used to display the add to cart button.
  Intended for product-form.liquid block.

  @param {string} can_add_to_cart - Whether the product can be added to the cart
  @param {string} add_to_cart_text - The text of the add to cart button
{%- enddoc -%}

{% liquid
  assign class = 'add-to-cart-button ' | append: block.settings.style_class
  assign id = 'BuyButtons-ProductSubmitButton-' | append: block.id
%}

<span
  {{ block.shopify_attributes }}
  style="--add-to-cart-font-case: {{ settings.button_text_case }};"
>
  {% render 'add-to-cart-button',
    id: id,
    class: class,
    can_add_to_cart: can_add_to_cart,
    product: closest.product,
    add_to_cart_text: add_to_cart_text
  %}
</span>

{% schema %}
{
  "name": "t:names.add_to_cart",
  "tag": null,
  "settings": [
    {
      "type": "select",
      "id": "style_class",
      "label": "t:settings.style",
      "options": [
        {
          "value": "button",
          "label": "t:options.primary"
        },
        {
          "value": "button-secondary",
          "label": "t:options.secondary"
        }
      ],
      "default": "button-secondary"
    }
  ]
}
{% endschema %}
