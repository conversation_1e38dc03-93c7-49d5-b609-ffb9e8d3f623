/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin language editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "names": {
    "404": "404",
    "borders": "Limites",
    "collapsible_row": "Linha recolhível",
    "colors": "Cores",
    "custom_section": "Secção personalizada",
    "icon": "Ícone",
    "logo_and_favicon": "Logótipo e favicon",
    "overlapping_blocks": "Blocos sobrepostos",
    "product_buy_buttons": "Botões de compra",
    "product_description": "Descrição",
    "product_price": "Preço",
    "product_variant_picker": "Seletor de variante",
    "slideshow": "Apresentação de diapositivos",
    "typography": "Tipografia",
    "video": "Vídeo",
    "slideshow_controls": "Controlos da apresentação de slides",
    "size": "Tamanho",
    "spacing": "Espaçamento",
    "product_recommendations": "Produtos recomendados",
    "product_media": "Conteúdo multimédia do produto",
    "featured_collection": "Coleção em destaque",
    "add_to_cart": "Adicionar ao carrinho",
    "email_signup": "Registo de e-mail",
    "submit_button": "Botão Submeter",
    "grid_layout_selector": "Seletor de esquema de grelha",
    "image": "Imagem",
    "list_items": "Itens da lista",
    "facets": "Facetas",
    "variants": "Variantes",
    "styles": "Estilos",
    "product_cards": "Cartões de produtos",
    "buttons": "Botões",
    "inputs": "Entradas",
    "primary_button": "Botão principal",
    "secondary_button": "Botão secundário",
    "popovers": "Pop-overs",
    "marquee": "Marcador",
    "pull_quote": "Obter orçamento",
    "contact_form": "Formulário de contacto",
    "featured_product": "Destaques do produto",
    "icons_with_text": "Ícones com texto",
    "alternating_content_rows": "Linhas alternadas",
    "product_list": "Lista de produtos",
    "spacer": "Espaçador",
    "accelerated_checkout": "Finalização da compra acelerada",
    "accordion": "Acordeão",
    "accordion_row": "Linha acordeão",
    "animations": "Animações",
    "announcement": "Anúncio",
    "announcement_bar": "Barra de anúncio",
    "badges": "Selos",
    "button": "Botão",
    "cart": "Carrinho",
    "cart_items": "Itens do carrinho",
    "cart_products": "Produtos do carrinho",
    "cart_title": "Carrinho",
    "collection": "Coleção",
    "collection_card": "Cartão de coleção",
    "collection_columns": "Colunas de coleção",
    "collection_container": "Coleção",
    "collection_description": "Descrição de coleção",
    "collection_image": "Imagem da coleção",
    "collection_info": "Informação de coleção",
    "collection_list": "Lista de coleções",
    "collections": "Coleções",
    "content": "Conteúdo",
    "content_grid": "Grelha de conteúdo",
    "details": "Detalhes",
    "divider": "Divisor",
    "filters": "Filtragem e ordenação",
    "follow_on_shop": "Seguir na Shop",
    "footer": "Rodapé",
    "footer_utilities": "Utilitários de rodapé",
    "group": "Grupo",
    "header": "Cabeçalho",
    "heading": "Título",
    "icons": "Ícones",
    "image_with_text": "Imagem com texto",
    "input": "Entrada",
    "logo": "Logótipo",
    "magazine_grid": "Grelha de revista",
    "media": "Conteúdo multimédia",
    "menu": "Menu",
    "mobile_layout": "Esquema para dispositivo móvel",
    "payment_icons": "Ícones de pagamento",
    "popup_link": "Ligação pop-up",
    "predictive_search": "Pesquisar pop-over",
    "predictive_search_empty": "Pesquisa preditiva vazia",
    "price": "Preço",
    "product": "Produto",
    "product_card": "Cartão de produto",
    "product_card_media": "Conteúdo multimédia",
    "product_card_rendering": "Renderização de cartão de produto",
    "product_grid": "Grelha",
    "product_grid_main": "Grelha de produtos",
    "product_image": "Imagem de produto",
    "product_information": "Informações de produto",
    "product_review_stars": "Estrelas de avaliação",
    "quantity": "Quantidade",
    "row": "Linha",
    "search": "Pesquisar",
    "section": "Secção",
    "selected_variants": "Variantes selecionadas",
    "shop_the_look": "Visual Shop",
    "slide": "Diapositivo",
    "social_media_links": "Ligações de redes sociais",
    "steps": "Passos",
    "summary": "Resumo",
    "swatches": "Paletas",
    "testimonials": "Depoimentos",
    "text": "Texto",
    "title": "Título",
    "utilities": "Utilitários",
    "search_input": "Entrada de pesquisa",
    "search_results": "Resultados da pesquisa",
    "read_only": "Só de leitura",
    "collection_title": "Título da coleção",
    "collections_bento": "Lista de coleções: Bento",
    "faq_section": "FAQ",
    "hero": "Hero",
    "jumbo_text": "Texto Jumbo",
    "view_all_button": "Ver tudo",
    "video_section": "Vídeo",
    "custom_liquid": "Liquid personalizado",
    "blog": "Blogue",
    "blog_post": "Publicação de blogue",
    "blog_posts": "Publicações no blogue",
    "caption": "Legenda",
    "collection_card_image": "Imagem",
    "collection_links": "Ligações de coleção",
    "collection_links_spotlight": "Ligações de coleção: Spotlight",
    "collection_links_text": "Ligações de coleção: Texto",
    "collections_carousel": "Lista de coleções: Carrossel",
    "collections_editorial": "Lista de coleções: Editorial",
    "collections_grid": "Lista de coleções: Grelha",
    "copyright": "Direitos de autor",
    "count": "Contagem",
    "divider_section": "Divisor",
    "drawers": "Gavetas",
    "editorial": "Editorial",
    "editorial_jumbo_text": "Editorial: Texto Jumbo",
    "hero_marquee": "Hero: Marcador",
    "input_fields": "Campos de entrada",
    "local_pickup": "Recolha local",
    "marquee_section": "Marcador",
    "media_with_text": "Conteúdo multimédia com texto",
    "page": "Página",
    "page_content": "Conteúdo",
    "page_layout": "Esquema da página",
    "policy_list": "Ligações para políticas",
    "prices": "Preços",
    "products_carousel": "Lista de produtos: Carrossel",
    "products_editorial": "Lista de produtos: Editorial",
    "products_grid": "Lista de produtos: Grelha",
    "social_link": "Ligação para redes sociais",
    "split_showcase": "Apresentação dividida",
    "variant_pickers": "Seletores de variantes",
    "pills": "Forma de comprimidos",
    "product_title": "Título do produto",
    "large_logo": "Logótipo grande",
    "product_list_button": "Botão Ver tudo",
    "product_inventory": "Inventário de produtos",
    "description": "Descrição"
  },
  "settings": {
    "alignment": "Alinhamento",
    "autoplay": "Reprodução automática",
    "background": "Fundo",
    "border_radius": "Raio do canto",
    "border_width": "Espessura do limite",
    "borders": "Limites",
    "bottom_padding": "Preenchimento inferior",
    "button": "Botão",
    "color": "Cor",
    "colors": "Cores",
    "content_alignment": "Alinhamento de conteúdo",
    "content_direction": "Direção do conteúdo",
    "content_position": "Posição do conteúdo",
    "cover_image_size": "Tamanho da imagem de capa",
    "cover_image": "Imagem de capa",
    "custom_minimum_height": "Altura mínima personalizada",
    "custom_width": "Largura personalizada",
    "enable_video_looping": "Loop de vídeo",
    "favicon": "Favicon",
    "font_family": "Família de tipo de letra",
    "gap": "Intervalo",
    "geometric_translate_y": "Tradução Y geométrica",
    "heading": "Cabeçalho",
    "icon": "Ícone",
    "image": "Imagem",
    "image_icon": "Ícone de imagem",
    "image_opacity": "Opacidade da imagem",
    "image_position": "Posição da imagem",
    "image_ratio": "Proporção de imagem",
    "label": "Etiqueta",
    "line_height": "Altura de linha",
    "link": "Ligação",
    "layout_gap": "Intervalo do esquema",
    "make_section_full_width": "Tornar a secção em largura total",
    "minimum_height": "Altura mínima",
    "opacity": "Opacidade",
    "overlay_opacity": "Opacidade de sobreposição",
    "padding": "Preenchimento",
    "primary_color": "Ligações",
    "product": "Produto",
    "section_width": "Largura da secção",
    "size": "Tamanho",
    "slide_spacing": "Intervalo do diapositivo",
    "slide_width": "Largura do diapositivo",
    "slideshow_fullwidth": "Diapositivos de largura total",
    "style": "Estilo",
    "text": "Texto",
    "text_case": "Caixa",
    "top_padding": "Preenchimento superior",
    "video": "Vídeo",
    "video_alt_text": "Texto alternativo",
    "video_loop": "Repetir vídeo",
    "video_position": "Posição do vídeo",
    "width": "Largura",
    "z_index": "Índice Z",
    "limit_content_width": "Limitar largura do conteúdo",
    "color_scheme": "Esquema de cores",
    "inherit_color_scheme": "Herdar esquema de cores",
    "product_count": "Contagem de produtos",
    "product_type": "Tipo de produto",
    "content_width": "Largura do conteúdo",
    "collection": "Coleção",
    "enable_sticky_content": "Conteúdo fixador no ambiente de trabalho",
    "error_color": "Erro",
    "success_color": "Sucesso",
    "primary_font": "Tipo de letra principal",
    "secondary_font": "Tipo de letra secundário",
    "tertiary_font": "Tipo de letra terciário",
    "columns": "Colunas",
    "items_to_show": "Itens a apresentar",
    "layout": "Esquema",
    "layout_type": "Tipo",
    "show_grid_layout_selector": "Mostrar seletor de esquema de grelha",
    "view_more_show": "Mostrar o botão Ver mais",
    "image_gap": "Intervalo de imagem",
    "width_desktop": "Largura para computador",
    "width_mobile": "Largura para dispositivo móvel",
    "border_style": "Estilo da borda",
    "height": "Altura",
    "thickness": "Espessura",
    "stroke": "Traço",
    "filter_style": "Filtrar estilo",
    "swatches": "Paletas",
    "quick_add_colors": "Adição rápida de cores",
    "divider_color": "Divisor",
    "border_opacity": "Opacidade de borda",
    "hover_background": "Fundo ao passar o cursor",
    "hover_borders": "Contorno ao passar o cursor",
    "hover_text": "Texto ao passar o cursor",
    "primary_hover_color": "Ligação ao passar o cursor",
    "primary_button_text": "Texto do botão principal",
    "primary_button_background": "Fundo do botão principal",
    "primary_button_border": "Contorno do botão principal",
    "secondary_button_text": "Texto do botão secundário",
    "secondary_button_background": "Fundo do botão secundário",
    "secondary_button_border": "Contorno do botão secundário",
    "shadow_color": "Sombra",
    "limit_media_to_screen_height": "Ajustar à altura do ecrã",
    "mobile_logo_image": "Logótipo móvel",
    "video_autoplay": "Reprodução automática",
    "video_cover_image": "Imagem de capa",
    "video_external_url": "URL",
    "video_source": "Fonte",
    "first_row_media_position": "Posição do conteúdo multimédia na primeira linha",
    "background_color": "Cor de fundo",
    "hide_padding": "Ocultar preenchimento",
    "logo_font": "Tipo de letra do logótipo",
    "size_mobile": "Tamanho para móvel",
    "pixel_size_mobile": "Tamanho em píxeis",
    "percent_size_mobile": "Tamanho em percentagem",
    "unit": "Unidade",
    "custom_mobile_size": "Tamanho para móvel personalizado",
    "fixed_height": "Altura de píxel",
    "fixed_width": "Largura de píxel",
    "percent_height": "Altura em percentagem",
    "percent_width": "Largura em percentagem",
    "percent_size": "Tamanho em percentagem",
    "pixel_size": "Tamanho em píxeis",
    "accordion": "Acordeão",
    "aspect_ratio": "Relação de altura/largura",
    "auto_rotate_announcements": "Rotação automática de anúncios",
    "auto_rotate_slides": "Rotação automática de diapositivos",
    "badge_corner_radius": "Raio do canto",
    "badge_position": "Posição nos cartões",
    "badge_sale_color_scheme": "Venda",
    "badge_sold_out_color_scheme": "Esgotado",
    "behavior": "Comportamento",
    "blur": "Sombra desfocada",
    "border": "Limite",
    "bottom": "Inferior",
    "card_image_height": "Altura da imagem de produto",
    "carousel_on_mobile": "Carrossel em dispositivo móvel",
    "cart_count": "Contagem de carrinhos",
    "cart_items": "Itens do carrinho",
    "cart_related_products": "Produtos relacionados",
    "cart_title": "Carrinho",
    "cart_total": "Total do carrinho",
    "cart_type": "Tipo",
    "case": "Caixa",
    "checkout_buttons": "Botões de finalização de compra acelerada",
    "collection_list": "Coleções",
    "collection_templates": "Modelos de coleção",
    "content": "Conteúdo",
    "corner_radius": "Raio do canto",
    "country_region": "País/região",
    "currency_code": "Código da moeda",
    "custom_height": "Altura personalizada",
    "desktop_height": "Altura para computador",
    "direction": "Direção",
    "display": "Exibição",
    "divider_thickness": "Espessura do divisor",
    "divider": "Divisor",
    "dividers": "Divisores",
    "drop_shadow": "Sombra projetada",
    "empty_state_collection_info": "Apresentado antes da introdução de uma pesquisa",
    "empty_state_collection": "Coleção de estado vazio",
    "enable_filtering": "Filtros",
    "enable_grid_density": "Controlo de esquema de grelha",
    "enable_sorting": "Ordenação",
    "enable_zoom": "Ativar zoom",
    "equal_columns": "Colunas iguais",
    "expand_first_group": "Expandir primeiro grupo",
    "extend_media_to_screen_edge": "Estender conteúdo multimédia até à borda do ecrã",
    "extend_summary": "Estender até à borda do ecrã",
    "extra_large": "Extra grande",
    "extra_small": "Extra pequeno",
    "flag": "Sinalizar",
    "font_price": "Tipo de letra do preço",
    "font_weight": "Peso do tipo de letra",
    "font": "Tipo de letra",
    "full_width_first_image": "Largura total primeira imagem",
    "full_width_on_mobile": "Largura total em dispositivo móvel",
    "heading_preset": "Predefinição de título",
    "hide_unselected_variant_media": "Ocultar conteúdo multimédia de variante não selecionada",
    "horizontal_gap": "Intervalo horizontal",
    "horizontal_offset": "Desvio horizontal da sombra",
    "hover_behavior": "Comportamento ao passar o cursor",
    "icon_background": "Fundo do ícone",
    "icons": "Ícones",
    "image_border_radius": "Raio de canto da imagem",
    "installments": "Prestações",
    "integrated_button": "Botão integrado",
    "language_selector": "Seletor de idioma",
    "large": "Grande",
    "left_padding": "Preenchimento esquerdo",
    "left": "Esquerda",
    "letter_spacing": "Espaçamento entre letras",
    "limit_product_details_width": "Limitar largura dos detalhes do produto",
    "link_preset": "Predefinição de ligação",
    "links": "Ligações",
    "logo": "Logótipo",
    "loop": "Repetição",
    "make_details_sticky_desktop": "Fixador para computador",
    "max_width": "Largura máx",
    "media_height": "Altura do conteúdo multimédia",
    "media_overlay": "Sobreposição do conteúdo multimédia",
    "media_position": "Posição do conteúdo multimédia",
    "media_type": "Tipo de conteúdo multimédia",
    "media_width": "Largura do conteúdo multimédia",
    "menu": "Menu",
    "mobile_columns": "Colunas em dispositivos móveis",
    "mobile_height": "Altura em dispositivo móvel",
    "mobile_quick_add": "Adição rápida de dispositivo móvel",
    "motion_direction": "Direção de movimento",
    "motion": "Movimento",
    "movement_direction": "Direção de movimento",
    "navigation_bar_color_scheme": "Esquema de cores da barra de navegação",
    "navigation_bar": "Barra de navegação",
    "navigation": "Navegação",
    "open_new_tab": "Abrir ligação em novo separador",
    "overlay_color": "Cor de sobreposição",
    "overlay": "Sobreposição",
    "padding_bottom": "Preenchimento inferior",
    "padding_horizontal": "Preenchimento horizontal",
    "padding_top": "Preenchimento superior",
    "page_width": "Largura da página",
    "pagination": "Paginação",
    "placement": "Posicionamento",
    "position": "Posição",
    "preset": "Predefinição",
    "product_cards": "Cartões de produtos",
    "product_pages": "Páginas de produtos",
    "product_templates": "Modelos de produto",
    "products": "Produtos",
    "quick_add": "Adição rápida",
    "ratio": "Proporção",
    "regular": "Normal",
    "review_count": "Contagem de avaliações",
    "right": "Direita",
    "row_height": "Altura da linha",
    "row": "Linha",
    "seller_note": "Permitir nota ao vendedor",
    "shape": "Forma",
    "show_as_accordion": "Mostrar como acordeão em dispositivo móvel",
    "show_sale_price_first": "Mostrar preço de saldo primeiro",
    "show_tax_info": "Informações tributárias",
    "show": "Mostrar",
    "small": "Pequeno",
    "speed": "Velocidade",
    "statement": "Extrato",
    "sticky_header": "Cabeçalho fixo",
    "text_hierarchy": "Hierarquia de texto",
    "text_presets": "Predefinições de texto",
    "title": "Título",
    "top": "Superior",
    "type": "Tipo",
    "type_preset": "Predefinição de texto",
    "underline_thickness": "Espessura do sublinhado",
    "variant_images": "Imagens da variante",
    "vendor": "Fornecedor",
    "vertical_gap": "Intervalo vertical",
    "vertical_offset": "Desvio vertical da sombra",
    "vertical_on_mobile": "Vertical para dispositivo móvel",
    "view_all_as_last_card": "\"Ver tudo\" como último cartão",
    "weight": "Peso",
    "wrap": "Envolvimento",
    "read_only": "Só de leitura",
    "always_stack_buttons": "Empilhar sempre botões",
    "custom_mobile_width": "Largura para dispositivo móvel personalizada",
    "gradient_direction": "Direção de gradiente",
    "overlay_style": "Estilo da sobreposição",
    "shadow_opacity": "Opacidade da sombra",
    "show_filter_label": "Etiquetas de texto para filtros aplicados",
    "show_swatch_label": "Etiquetas de texto para paletas",
    "transparent_background": "Fundo transparente",
    "account": "Conta",
    "align_baseline": "Alinhar baseline de texto",
    "add_discount_code": "Permitir descontos no carrinho",
    "background_overlay": "Sobreposição de fundo",
    "background_media": "Conteúdo multimédia de fundo",
    "border_thickness": "Espessura do limite",
    "bottom_row": "Linha inferior",
    "button_text_case": "Texto em maiúsculas/minúsculas",
    "button_text_weight": "Peso do texto",
    "card_size": "Tamanho do cartão",
    "auto_open_cart_drawer": "\"Adicionar ao carrinho\" abre automaticamente uma gaveta",
    "collection_count": "Número de coleções",
    "custom_liquid": "Código Liquid",
    "default": "Predefinição",
    "default_logo": "Logótipo padrão",
    "divider_width": "Largura do divisor",
    "headings": "Títulos",
    "hide_logo_on_home_page": "Ocultar logótipo na página inicial",
    "horizontal_padding": "Preenchimento horizontal",
    "inverse": "Invertido",
    "inverse_logo": "Logótipo invertido",
    "layout_style": "Estilo",
    "length": "Comprimento",
    "mobile_card_size": "Tamanho de cartão para dispositivo móvel",
    "mobile_pagination": "Paginação móvel",
    "open_row_by_default": "Abrir linha por predefinição",
    "page": "Página",
    "page_transition_enabled": "Transição de página",
    "right_padding": "Preenchimento direito",
    "search": "Pesquisar",
    "search_icon": "Ícone de pesquisa",
    "search_position": "Posição",
    "search_row": "Linha",
    "show_author": "Autor",
    "show_alignment": "Mostrar alinhamento",
    "show_count": "Mostrar contagem",
    "show_date": "Data",
    "show_pickup_availability": "Mostrar disponibilidade de recolha",
    "show_search": "Mostrar pesquisa",
    "use_inverse_logo": "Utilizar logótipo invertido",
    "vertical_padding": "Preenchimento vertical",
    "visibility": "Visibilidade",
    "product_corner_radius": "Raio de canto do produto",
    "card_corner_radius": "Raio de canto do cartão",
    "alignment_mobile": "Alinhamento em dispositivos móveis",
    "animation_repeat": "Repetir animação",
    "blurred_reflection": "Reflexo desfocado",
    "card_hover_effect": "Efeito ao passar o rato no cartão",
    "collection_title_case": "Formato de maiúsculas iniciais da coleção",
    "effects": "Efeitos",
    "inventory_threshold": "Limiar de stock baixo",
    "product_and_card_title_case": "Formato de maiúsculas iniciais do produto e cartão",
    "product_title_case": "Formato de maiúsculas iniciais do produto",
    "reflection_opacity": "Opacidade do reflexo",
    "show_inventory_quantity": "Mostrar quantidade de stock baixo",
    "text_label_case": "Formato de maiúsculas iniciais das etiquetas de texto",
    "transition_to_main_product": "Transição entre cartão de produto e página de produto",
    "show_second_image_on_hover": "Mostrar segunda imagem ao passar o rato",
    "media": "Conteúdo multimédia",
    "product_card_carousel": "Mostrar carrossel"
  },
  "options": {
    "adapt_to_image": "Adaptar à imagem",
    "apple": "Maçã",
    "arrow": "Seta",
    "auto": "Automático",
    "banana": "Banana",
    "bottle": "Garrafa",
    "box": "Caixa",
    "buttons": "Botões",
    "carrot": "Cenoura",
    "center": "Centro",
    "chat_bubble": "Bolha de conversa",
    "clipboard": "Área de transferência",
    "contain": "Contém",
    "counter": "Contador",
    "cover": "Capa",
    "custom": "Personalizado",
    "dairy_free": "Sem produtos lácteos",
    "dairy": "Produtos lácteos",
    "default": "Predefinição",
    "dropdowns": "Menus pendentes",
    "dots": "Pontos",
    "dryer": "Secador",
    "end": "Fim",
    "eye": "Olho",
    "facebook": "Facebook",
    "fill": "Preencher",
    "fire": "Fogo",
    "fit": "Ajustar",
    "full": "Completo",
    "full_and_page": "Fundo completo, conteúdo de largura de página",
    "gluten_free": "Sem glúten",
    "heading": "Título",
    "heart": "Coração",
    "horizontal": "Horizontal",
    "instagram": "Instagram",
    "iron": "Ferro",
    "landscape": "Horizontal",
    "large": "Grande",
    "leaf": "Folha",
    "leather": "Couro",
    "lg": "L",
    "lightning_bolt": "Relâmpago",
    "link": "Ligação",
    "lipstick": "Batom",
    "lock": "Cadeado",
    "lowercase": "minúsculas",
    "m": "M",
    "map_pin": "Marcador de mapa",
    "medium": "Médio",
    "none": "Nenhum(a)",
    "numbers": "Números",
    "nut_free": "Sem frutos de casca rija",
    "outline": "Contorno",
    "page": "Página",
    "pants": "Calças",
    "paw_print": "Marca de pata",
    "pepper": "Pimenta",
    "perfume": "Perfume",
    "pinterest": "Pinterest",
    "plane": "Avião",
    "plant": "Planta",
    "portrait": "Vertical",
    "price_tag": "Etiqueta de preço",
    "question_mark": "Ponto de interrogação",
    "recycle": "Reciclar",
    "return": "Devolução",
    "ruler": "Régua",
    "s": "S",
    "sentence": "Frase",
    "serving_dish": "Prato",
    "shirt": "Camisa",
    "shoe": "Sapato",
    "silhouette": "Silhueta",
    "small": "Pequeno",
    "snapchat": "Snapchat",
    "snowflake": "Floco de neve",
    "solid": "Sólido",
    "space_between": "Espaço entre",
    "square": "Quadrado",
    "star": "Estrela",
    "start": "Início",
    "stopwatch": "Cronómetro",
    "tiktok": "TikTok",
    "truck": "Camião",
    "tumblr": "Tumblr",
    "twitter": "X (Twitter)",
    "uppercase": "Maiúsculas",
    "vertical": "Vertical",
    "vimeo": "Vimeo",
    "washing": "Lavar",
    "circle": "Círculo",
    "swatches": "Paletas",
    "full_and_page_offset_left": "Fundo completo, conteúdo limitado à largura da página, ajustado à esquerda",
    "full_and_page_offset_right": "Fundo completo, conteúdo limitado à largura da página, ajustado à direita",
    "offset_left": "Ajustado à esquerda",
    "offset_right": "Ajustado à direita",
    "page_center_aligned": "Página, alinhado ao centro",
    "page_left_aligned": "Página, alinhado à esquerda",
    "page_right_aligned": "Página, alinhado à direita",
    "button": "Botão",
    "caption": "Legenda",
    "h1": "Título 1",
    "h2": "Título 2",
    "h3": "Título 3",
    "h4": "Título 4",
    "h5": "Título 5",
    "h6": "Título 6",
    "paragraph": "Parágrafo",
    "primary": "Principal",
    "secondary": "Secundário",
    "tertiary": "Terciário",
    "chevron_left": "Divisa para a esquerda",
    "chevron_right": "Divisa para a direita",
    "diamond": "Diamante",
    "grid": "Grelha",
    "parallelogram": "Paralelograma",
    "rounded": "Arredondado",
    "fit_content": "Ajustar",
    "pills": "Forma de comprimidos",
    "heavy": "Grosso",
    "thin": "Fino",
    "drawer": "Gaveta",
    "preview": "Pré-visualização",
    "text": "Texto",
    "video_uploaded": "Carregado",
    "video_external_url": "URL externo",
    "up": "Para cima",
    "down": "Para baixo",
    "gradient": "Gradiente",
    "aspect_ratio": "Relação de altura/largura",
    "fixed": "Fixo",
    "pixel": "Píxeis",
    "percent": "Percentagem",
    "above_carousel": "Por cima do carrossel",
    "all": "Tudo",
    "always": "Sempre",
    "arrows_large": "Setas grandes",
    "arrows": "Setas",
    "balance": "Saldo",
    "bento": "Bento",
    "black": "Preto",
    "bluesky": "Bluesky",
    "body_large": "Corpo (Grande)",
    "body_regular": "Corpo (Normal)",
    "body_small": "Corpo (Pequeno)",
    "bold": "Negrito",
    "bottom_left": "Canto inferior esquerdo",
    "bottom_right": "Canto inferior direito",
    "bottom": "Inferior",
    "capitalize": "Capitalizar",
    "caret": "Caret",
    "carousel": "Carrossel",
    "check_box": "Caixa de seleção",
    "chevron_large": "Divisas grandes",
    "chevron": "Divisa",
    "chevrons": "Divisas",
    "classic": "Clássico",
    "collection_images": "Imagens da coleção",
    "color": "Cor",
    "complementary": "Complementar",
    "dissolve": "Dissolver",
    "dotted": "Pontilhado",
    "editorial": "Editorial",
    "extra_large": "Extra grande",
    "extra_small": "Extra pequeno",
    "featured_collections": "Coleções em destaque",
    "featured_products": "Produtos em destaque",
    "font_primary": "Principal",
    "font_secondary": "Secundário",
    "font_tertiary": "Terciário",
    "forward": "Avançar",
    "full_screen": "Ecrã inteiro",
    "heading_extra_large": "Título (Extra grande)",
    "heading_extra_small": "Título (Extra pequeno)",
    "heading_large": "Título (Grande)",
    "heading_regular": "Título (Normal)",
    "heading_small": "Título (Pequeno)",
    "icon": "Ícone",
    "image": "Imagem",
    "input": "Entrada",
    "inside_carousel": "Carrossel interior",
    "inverse_large": "Invertido grande",
    "inverse": "Invertido",
    "large_arrows": "Setas grandes",
    "large_chevrons": "Divisas grandes",
    "left": "Esquerda",
    "light": "Claro",
    "linkedin": "LinkedIn",
    "loose": "Solto",
    "media_first": "Conteúdo multimédia primeiro",
    "media_second": "Conteúdo multimédia segundo",
    "modal": "Modal",
    "narrow": "Estreito",
    "never": "Nunca",
    "next_to_carousel": "Junto ao carrossel",
    "normal": "Normal",
    "nowrap": "Sem passagem de linha",
    "off_media": "Fora do conteúdo multimédia",
    "on_media": "No conteúdo multimédia",
    "on_scroll_up": "Ao rodar a roda do rato para cima",
    "one_half": "1/2",
    "one_number": "1",
    "one_third": "1/3",
    "pill": "Comprimido",
    "plus": "Plus",
    "pretty": "Bonito",
    "price": "Preço",
    "primary_style": "Estilo primário",
    "rectangle": "Retângulo",
    "regular": "Normal",
    "related": "Relacionado",
    "reverse": "Inverter",
    "rich_text": "Texto formatado",
    "right": "Direita",
    "secondary_style": "Estilo secundário",
    "semibold": "Seminegrito",
    "shaded": "Sombreado",
    "show_second_image": "Mostrar segunda imagem",
    "single": "Único",
    "slide_left": "Deslizar para a esquerda",
    "slide_up": "Deslizar para cima",
    "spotify": "Spotify",
    "stack": "Stack",
    "text_only": "Só texto",
    "threads": "Threads",
    "thumbnails": "Miniaturas",
    "tight": "Apertado",
    "top_left": "Canto superior esquerdo",
    "top_right": "Canto superior direito",
    "top": "Superior",
    "two_number": "2",
    "two_thirds": "2/3",
    "underline": "Sublinhado",
    "video": "Vídeo",
    "wide": "Largo",
    "youtube": "YouTube",
    "accent": "Destaque",
    "below_image": "Abaixo da imagem",
    "body": "Corpo",
    "button_primary": "Botão principal",
    "button_secondary": "Botão secundário",
    "compact": "Compacta",
    "crop_to_fit": "Recortar para ajustar",
    "hidden": "Oculto",
    "hint": "Dica",
    "maintain_aspect_ratio": "Manter proporção",
    "off": "Desativar",
    "on_image": "Na imagem",
    "social_bluesky": "Social: Bluesky",
    "social_facebook": "Social: Facebook",
    "social_instagram": "Social: Instagram",
    "social_linkedin": "Social: LinkedIn",
    "social_pinterest": "Social: Pinterest",
    "social_snapchat": "Social: Snapchat",
    "social_spotify": "Social: Spotify",
    "social_threads": "Social: Threads",
    "social_tiktok": "Social: TikTok",
    "social_tumblr": "Social: Tumblr",
    "social_twitter": "Social: X (Twitter)",
    "social_whatsapp": "Social: WhatsApp",
    "social_vimeo": "Social: Vimeo",
    "social_youtube": "Social: YouTube",
    "spotlight": "Spotlight",
    "standard": "Normal",
    "subheading": "Subtítulo",
    "blur": "Desfocar",
    "lift": "Elevar",
    "reveal": "Revelar",
    "scale": "Expandir",
    "subtle_zoom": "Zoom"
  },
  "content": {
    "advanced": "Avançado",
    "background_image": "Imagem de fundo",
    "background_video": "Vídeo de fundo",
    "block_size": "Tamanho do bloco",
    "borders": "Limites",
    "describe_the_video_for": "Descreve o vídeo para que seja acessível a clientes que usam leitores de ecrã. [Saber mais](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)",
    "section_size": "Tamanho da secção",
    "slideshow_width": "Largura do diapositivo",
    "typography": "Tipografia",
    "width_is_automatically_optimized": "A largura é otimizada automaticamente para dispositivos móveis.",
    "complementary_products": "Os produtos complementares têm de ser configurados utilizando a aplicação Search & Discovery. [Saber mais](https://help.shopify.com/manual/online-store/search-and-discovery)",
    "mobile_column_optimization": "As colunas serão otimizadas automaticamente para dispositivos móveis",
    "content_width": "Largura do conteúdo apenas aplicável se a largura da secção estiver definida para largura total.",
    "adjustments_affect_all_content": "Aplicável a todo o conteúdo neste bloco",
    "responsive_font_sizes": "Os tamanhos são dimensionados automaticamente para todos os tamanhos de ecrã",
    "buttons": "Botões",
    "swatches": "Paletas",
    "variant_settings": "Definições de variante",
    "background": "Fundo",
    "cards_layout": "Esquema de cartões",
    "section_layout": "Esquema de secção",
    "mobile_size": "Tamanho para móvel",
    "appearance": "Aspeto",
    "arrows": "Setas",
    "body_size": "Tamanho do corpo",
    "bottom_row_appearance": "Aspeto da linha inferior",
    "carousel_navigation": "Navegação tipo carrossel",
    "carousel_pagination": "Paginação tipo carrossel",
    "copyright": "Direitos de autor",
    "edit_logo_in_theme_settings": "Editar logótipo em [definições de tema](/editor?context=theme&category=logo%20and%20favicon)",
    "edit_price_in_theme_settings": "Editar formatação de preço em [definições de tema](/editor?context=theme&category=currency%20code)",
    "edit_variants_in_theme_settings": "Editar estilo de variante em [definições de tema](/editor?context=theme&category=variants)",
    "email_signups_create_customer_profiles": "Adição de registos [perfis de clientes](https://help.shopify.com/manual/customers)",
    "follow_on_shop_eligiblity": "Para o botão aparecer, o canal Shop tem de ser instalado e o Shop Pay tem de ser ativado. [Saber mais](https://help.shopify.com/en/manual/online-store/themes/customizing-themes/add-shop-buttons)",
    "fonts": "Tipos de letra",
    "grid": "Grelha",
    "heading_size": "Tamanho do título",
    "image": "Imagem",
    "input": "Entrada",
    "layout": "Esquema",
    "link": "Ligação",
    "link_padding": "Preenchimento de ligação",
    "localization": "Localização",
    "logo": "Logótipo",
    "margin": "Margem",
    "media": "Conteúdo multimédia",
    "media_1": "Conteúdo multimédia 1",
    "media_2": "Conteúdo multimédia 2",
    "menu": "Menu",
    "mobile_layout": "Esquema para dispositivo móvel",
    "padding": "Preenchimento",
    "padding_desktop": "Preenchimento para computador",
    "paragraph": "Parágrafo",
    "policies": "Políticas",
    "popup": "Pop-up",
    "search": "Pesquisar",
    "size": "Tamanho",
    "social_media": "Redes sociais",
    "submit_button": "Botão Submeter",
    "text_presets": "Predefinições de texto",
    "transparent_background": "Fundo transparente",
    "typography_primary": "Tipografia principal",
    "typography_secondary": "Tipografia secundária",
    "typography_tertiary": "Tipografia terciária",
    "mobile_width": "Largura para dispositivo móvel",
    "width": "Largura",
    "carousel": "Carrossel",
    "colors": "Cores",
    "collection_page": "Página de coleção",
    "copyright_info": "Saiba como [editar a sua declaração de direitos de autor](https://help.shopify.com/manual/online-store/themes/customizing-themes/remove-powered-by-shopify-message)",
    "customer_account": "Conta de cliente",
    "edit_empty_state_collection_in_theme_settings": "Editar coleção de estado vazio em [definições de tema](/editor?context=theme&category=search)",
    "grid_layout": "Esquema de grelha",
    "home_page": "Página inicial",
    "images": "Imagens",
    "inverse_logo_info": "Utilizado quando o fundo transparente do cabeçalho é definido para Invertido",
    "manage_customer_accounts": "[Gerir visibilidade](/admin/settings/customer_accounts) nas definições de contas de cliente. As contas legadas não são suportadas.",
    "manage_policies": "[Gerir políticas](/admin/settings/legal)",
    "product_page": "Página do produto",
    "text": "Texto",
    "thumbnails": "Miniaturas",
    "visibility": "Visibilidade",
    "visible_if_collection_has_more_products": "Visível se a coleção tiver mais produtos do que os mostrados",
    "app_required_for_ratings": "É necessária uma aplicação para as classificações de produto. [Saber mais](https://help.shopify.com/manual/apps)",
    "icon": "Ícone",
    "resource_reference_collection_card": "Apresenta coleção da secção principal",
    "resource_reference_collection_card_image": "Apresenta imagem da coleção principal",
    "resource_reference_collection_title": "Apresenta título da coleção principal",
    "resource_reference_product": "Liga automaticamente ao produto principal",
    "resource_reference_product_card": "Apresenta produto da secção principal",
    "resource_reference_product_inventory": "Apresenta inventário do produto principal",
    "resource_reference_product_price": "Apresenta preço do produto principal",
    "resource_reference_product_recommendations": "Apresenta recomendações baseadas no produto principal",
    "resource_reference_product_review": "Apresenta avaliações do produto principal",
    "resource_reference_product_swatches": "Apresenta amostras do produto principal",
    "resource_reference_product_title": "Apresenta título do produto principal",
    "resource_reference_product_variant_picker": "Apresenta variantes do produto principal",
    "resource_reference_product_media": "Apresenta multimédia do produto principal"
  },
  "html_defaults": {
    "share_information_about_your": "<p>Partilhe informações sobre a sua marca com os clientes. Descreva um produto, faça comunicados ou dê as boas-vindas aos clientes da loja.</p>"
  },
  "text_defaults": {
    "button_label": "Comprar agora",
    "collapsible_row": "Linha recolhível",
    "heading": "Título",
    "email_signup_button_label": "Subscrever",
    "accordion_heading": "Título acordeão",
    "contact_form_button_label": "Submeter",
    "popup_link": "Ligação pop-up",
    "sign_up": "Registe-se",
    "welcome_to_our_store": "Bem-vindo à nossa loja",
    "be_bold": "Seja arrojado.",
    "shop_our_latest_arrivals": "Compre as nossas novidades mais recentes!"
  },
  "info": {
    "video_alt_text": "Descreva o vídeo para utilizadores de tecnologia de apoio",
    "video_autoplay": "O som dos vídeos estará desativado por predefinição",
    "video_external": "Utilizar um URL do YouTube ou Vimeo",
    "carousel_layout_on_mobile": "O carrossel é utilizado num dispositivo móvel",
    "link_info": "Opcional: torna o ícone clicável",
    "carousel_hover_behavior_not_supported": "Movimento \"Carrossel\" de passar o cursor não é suportado quando o tipo \"Carrossel\" é selecionado no nível de secção",
    "grid_layout_on_mobile": "O esquema de grelha é utilizado para dispositivos móveis",
    "logo_font": "Apenas aplicável quando não é selecionado um logótipo",
    "checkout_buttons": "Permite que os compradores finalizem a compra mais rapidamente e pode melhorar a conversão. [Saber mais](https://help.shopify.com/manual/online-store/dynamic-checkout)",
    "custom_heading": "Título personalizado",
    "edit_presets_in_theme_settings": "Editar predefinições em [definições de tema](/editor?context=theme&category=typography)",
    "enable_filtering_info": "Personalizar filtros com a [aplicação Search & Discovery](https://help.shopify.com/manual/online-store/search-and-discovery/filters)",
    "manage_countries_regions": "[Gerir países/regiões](/admin/settings/markets)",
    "manage_languages": "[Gerir idiomas](/admin/settings/languages)",
    "transparent_background": "Reveja cada modelo em que é aplicado um fundo transparente para melhor legibilidade",
    "aspect_ratio_adjusted": "Ajustado em alguns esquemas",
    "auto_open_cart_drawer": "Quando ativado, o painel deslizante do carrinho abre-se automaticamente quando um produto é adicionado ao carrinho.",
    "custom_liquid": "Adicione fragmentos de aplicação ou outro código para criar personalizações avançadas. [Saber mais](https://shopify.dev/docs/api/liquid)",
    "pills_usage": "Utilizado para filtros aplicados, códigos de desconto e sugestões de pesquisa",
    "applies_on_image_only": "Apenas aplicável às imagens",
    "hover_effects": "Aplicável a cartões de produto e coleção"
  },
  "categories": {
    "basic": "Basic",
    "collection": "Coleção",
    "collection_list": "Lista de coleções",
    "footer": "Rodapé",
    "forms": "Formulários",
    "header": "Cabeçalho",
    "layout": "Esquema",
    "links": "Ligações",
    "product": "Produto",
    "product_list": "Lista de produtos",
    "banners": "Faixas",
    "collections": "Coleções",
    "custom": "Personalizado",
    "decorative": "Decorativo",
    "products": "Produtos",
    "other_sections": "Outro",
    "storytelling": "Contar uma história"
  }
}
