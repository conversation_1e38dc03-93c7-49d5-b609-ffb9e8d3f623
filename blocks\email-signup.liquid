{% # import schema from '../schemas/blocks/email-signup' %}

<div
  class="email-signup-block size-style spacing-style"
  style="{% render 'size-style', settings: block.settings %}{% render 'spacing-style', settings: block.settings %}"
  {{ block.shopify_attributes }}
>
  {%- form 'customer', class: 'email-signup__form spacing-style' %}
    <div
      class="email-signup__input-group {% if block.settings.inherit_color_scheme == false %}color-{{ block.settings.color_scheme }}{% endif %}"
      style="--border-width: {{ block.settings.border_width }}px; --border-radius: {{ block.settings.border_radius }}px;"
    >
      <label
        for="EmailInput-{{ block.id }}"
        class="visually-hidden"
      >
        {{ 'blocks.email_signup.label' | t }}
      </label>
      <input
        id="EmailInput-{{ block.id }}"
        class="email-signup__input email-signup__input--{{ block.settings.border_style }} {{ block.settings.input_type_preset }} {{ block.settings.border_variation }}"
        type="email"
        name="contact[email]"
        autocorrect="off"
        autocapitalize="off"
        autocomplete="email"
        placeholder="{{ 'blocks.email_signup.placeholder' | t }}"
        required
        {% if form.errors %}
          autofocus
          aria-invalid="true"
          aria-describedby="Email-signup__message-error-{{ block.id }}"
        {% elsif form.posted_successfully? %}
          aria-describedby="Email-signup__message-success-{{ block.id }}"
        {% endif %}
      >
      <button
        class="email-signup__button {% if block.settings.integrated_button or block.settings.border_style == 'underline' %}email-signup__button--integrated{% endif %} email-signup__button--{{ block.settings.display_type }} {{ block.settings.button_type_preset }} {{ block.settings.style_class }} {{ block.settings.style_class }}--{{ block.id }} {% if block.settings.inherit_color_scheme == false %}color-{{ block.settings.color_scheme }}{% endif %}"
        {% if block.settings.display_type != 'text' %}
          aria-label="{{ 'actions.sign_up' | t }}"
        {% endif %}
      >
        {% if block.settings.display_type == 'text' %}
          {{ block.settings.label }}
        {% else %}
          <svg
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
            focusable="false"
            aria-hidden="true"
            class="email-signup__button-icon"
          >
            {% render 'icon', icon: 'arrow' %}
          </svg>
        {% endif %}
      </button>
    </div>

    {%- if form.errors -%}
      <div
        id="Email-signup__message-error-{{ block.id }}"
        class="email-signup__message email-signup__message--error"
        tabindex="-1"
      >
        <svg
          viewBox="0 0 20 20"
          xmlns="http://www.w3.org/2000/svg"
          focusable="false"
          aria-hidden="true"
          class="icon-error"
        >
          {%- render 'icon', icon: 'error'  -%}
        </svg>
        {{- form.errors.translated_fields.email | capitalize }}
        {{ form.errors.messages.email -}}
      </div>
    {%- endif -%}

    {%- if form.posted_successfully? -%}
      <div
        id="Email-signup__message-success-{{ block.id }}"
        class="email-signup__message email-signup__message--success"
        tabindex="-1"
      >
        <span class="svg-wrapper icon-success">
          {{ 'icon-checkmark.svg' | inline_asset_content }}
        </span>
        <p class="email-signup__message-text">{{ 'blocks.email_signup.success' | t }}</p>
      </div>
    {%- endif -%}
  {% endform %}
</div>

{% stylesheet %}
  .email-signup-block {
    @media screen and (max-width: 749px) {
      width: 100%;
    }
  }

  .email-signup__form {
    display: flex;
    flex-direction: column;
  }

  .email-signup__input-group {
    position: relative;
    display: grid;
    grid-template-columns: 1fr auto;
    background-color: transparent;
  }

  .email-signup__input-group:not(:has(.email-signup__button--integrated)) {
    gap: var(--gap-xs);
  }

  .email-signup__input-group:not(:has(.email-signup__button--arrow)) {
    @media screen and (max-width: 749px) {
      grid-template-columns: 1fr;
    }
  }

  .email-signup__input {
    width: 100%;
    border-width: var(--border-width);
    border-radius: var(--border-radius);
    border-style: solid;
    border-color: var(--color-input-border);
  }

  .email-signup__button {
    white-space: nowrap;
    padding: 0;

    @media screen and (max-width: 749px) {
      width: 100%;
    }
  }

  .email-signup__input,
  .email-signup__button--text {
    padding: var(--padding-lg) var(--padding-3xl);
  }

  .email-signup__input-group .email-signup__input--underline {
    --box-shadow-color: var(--color-input-border);
    --box-shadow-multiplier: 1;
    color: var(--color-input-text);
    background-color: transparent;
    padding: 12px 0;
    border: none;
    border-radius: 0;
    box-shadow: 0px calc(var(--border-width) * var(--box-shadow-multiplier)) 0px var(--box-shadow-color);
    transition: box-shadow var(--animation-values);

    &:focus-visible {
      --box-shadow-multiplier: 1.75;
      --box-shadow-color: var(--color-input-text);
      outline: none;
    }
  }

  .email-signup__input::placeholder {
    color: rgb(from var(--color-input-text) r g b / 70%);
  }

  .email-signup__input-group .email-signup__input--none {
    color: var(--color-input-text);
    background-color: var(--color-input-background);
    border: none;
  }

  .email-signup__input:has(+ .email-signup__button--arrow),
  .email-signup__input:has(+ .email-signup__button--integrated) {
    @media screen and (max-width: 749px) {
      text-align: left;
    }
  }

  .email-signup__button--arrow {
    aspect-ratio: 1;
    padding-inline: var(--padding-xs);

    &:not(.email-signup__button--integrated) {
      width: auto;
    }
  }

  .email-signup__button--integrated {
    --button-offset: var(--margin-xs);
    position: absolute;
    height: calc(100% - (var(--button-offset) * 2) - (var(--border-width) * 2));
    right: calc(var(--button-offset) + var(--border-width));
    top: calc(var(--button-offset) + var(--border-width));

    @media screen and (max-width: 749px) {
      width: fit-content;
    }

    &.email-signup__button--text {
      padding: 0 var(--padding-3xl);
    }

    &.email-signup__button--text.button-unstyled {
      padding: 0 var(--padding-xl);
    }

    &.button-unstyled {
      border-radius: var(--border-radius);
    }

    > .email-signup__button-icon {
      padding: 0;
    }
  }

  .email-signup__input--underline + .email-signup__button--integrated {
    right: 0;
    --button-offset: calc(10% - var(--border-width));

    &.email-signup__button--text.button-unstyled {
      padding: 0;
    }

    &.button-unstyled {
      border-radius: 0;
    }
  }

  .email-signup__button:not(.button-unstyled) {
    background-color: var(--button-background-color);
    color: var(--button-color);
    font-weight: var(--button-font-weight-primary);
    text-transform: var(--button-text-case-primary);
  }

  .email-signup__button.button-secondary {
    font-weight: var(--button-font-weight-secondary);
    text-transform: var(--button-text-case-secondary);
  }

  .email-signup__button.button-unstyled {
    background-color: transparent;
    color: var(--color-input-text);
  }

  .email-signup__button.button-unstyled:hover {
    color: rgb(from var(--color-input-text) r g b / 70%);
    cursor: pointer;
  }

  .email-signup__button-icon {
    fill: currentcolor;
    padding: 5px;

    @media screen and (max-width: 749px) {
      padding: 0;
      align-self: center;
      justify-self: center;
      width: var(--icon-size-lg);
      height: var(--icon-size-lg);
    }
  }

  .email-signup__message {
    display: flex;
    align-items: center;
    gap: var(--gap-xs);
  }

  .email-signup__message-text {
    margin: 0;
  }
{% endstylesheet %}

{% schema %}
{
  "name": "t:names.email_signup",
  "tag": null,
  "settings": [
    {
      "type": "paragraph",
      "content": "t:content.email_signups_create_customer_profiles"
    },
    {
      "type": "select",
      "id": "width",
      "label": "t:settings.width",
      "options": [
        {
          "value": "fill",
          "label": "t:options.fill"
        },
        {
          "value": "custom",
          "label": "t:options.custom"
        }
      ],
      "default": "fill"
    },
    {
      "type": "range",
      "id": "custom_width",
      "label": "t:settings.custom_width",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "%",
      "default": 100,
      "visible_if": "{{ block.settings.width == \"custom\" }}"
    },
    {
      "type": "checkbox",
      "id": "inherit_color_scheme",
      "label": "t:settings.inherit_color_scheme",
      "default": true
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:settings.color_scheme",
      "default": "primary",
      "visible_if": "{{ block.settings.inherit_color_scheme == false }}"
    },
    {
      "type": "header",
      "content": "t:content.input"
    },
    {
      "type": "select",
      "id": "border_style",
      "label": "t:settings.border",
      "options": [
        {
          "value": "all",
          "label": "t:options.all"
        },
        {
          "value": "underline",
          "label": "t:options.underline"
        },
        {
          "value": "none",
          "label": "t:options.none"
        }
      ],
      "default": "all"
    },
    {
      "type": "range",
      "id": "border_width",
      "label": "t:settings.border_width",
      "min": 0,
      "max": 4,
      "step": 0.5,
      "unit": "px",
      "default": 1,
      "visible_if": "{{ block.settings.border_style != \"none\" }}"
    },
    {
      "type": "range",
      "id": "border_radius",
      "label": "t:settings.border_radius",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 100,
      "visible_if": "{{ block.settings.border_style != \"underline\" }}"
    },
    {
      "type": "select",
      "id": "input_type_preset",
      "label": "t:settings.type_preset",
      "options": [
        {
          "value": "",
          "label": "t:options.default"
        },
        {
          "value": "paragraph",
          "label": "t:options.paragraph"
        },
        {
          "value": "h1",
          "label": "t:options.h1"
        },
        {
          "value": "h2",
          "label": "t:options.h2"
        },
        {
          "value": "h3",
          "label": "t:options.h3"
        },
        {
          "value": "h4",
          "label": "t:options.h4"
        },
        {
          "value": "h5",
          "label": "t:options.h5"
        },
        {
          "value": "h6",
          "label": "t:options.h6"
        }
      ],
      "default": "paragraph",
      "info": "t:info.edit_presets_in_theme_settings"
    },
    {
      "type": "header",
      "content": "t:content.submit_button"
    },
    {
      "type": "select",
      "id": "style_class",
      "label": "t:settings.style",
      "options": [
        {
          "value": "button",
          "label": "t:options.primary"
        },
        {
          "value": "button-secondary",
          "label": "t:options.secondary"
        },
        {
          "value": "button-unstyled",
          "label": "t:options.link"
        }
      ],
      "default": "button"
    },
    {
      "type": "select",
      "id": "display_type",
      "label": "t:settings.display",
      "options": [
        {
          "value": "text",
          "label": "t:options.text"
        },
        {
          "value": "arrow",
          "label": "t:options.arrow"
        }
      ],
      "default": "text"
    },
    {
      "type": "text",
      "id": "label",
      "label": "t:settings.label",
      "default": "t:text_defaults.sign_up",
      "visible_if": "{{ block.settings.display_type == \"text\" }}"
    },
    {
      "type": "checkbox",
      "id": "integrated_button",
      "label": "t:settings.integrated_button",
      "default": false,
      "visible_if": "{{ block.settings.border_style != \"underline\" }}"
    },
    {
      "type": "select",
      "id": "button_type_preset",
      "label": "t:settings.type_preset",
      "options": [
        {
          "value": "",
          "label": "t:options.default"
        },
        {
          "value": "paragraph",
          "label": "t:options.paragraph"
        },
        {
          "value": "h1",
          "label": "t:options.h1"
        },
        {
          "value": "h2",
          "label": "t:options.h2"
        },
        {
          "value": "h3",
          "label": "t:options.h3"
        },
        {
          "value": "h4",
          "label": "t:options.h4"
        },
        {
          "value": "h5",
          "label": "t:options.h5"
        },
        {
          "value": "h6",
          "label": "t:options.h6"
        }
      ],
      "default": "paragraph",
      "info": "t:info.edit_presets_in_theme_settings",
      "visible_if": "{{ block.settings.display_type == \"text\" }}"
    },
    {
      "type": "header",
      "content": "t:content.padding"
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "t:settings.top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "t:settings.bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-inline-start",
      "label": "t:settings.left",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-inline-end",
      "label": "t:settings.right",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "t:names.email_signup",
      "category": "t:categories.forms"
    }
  ]
}
{% endschema %}
