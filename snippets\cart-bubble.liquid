{%- doc -%}
  @param [limit] - {number}
  @param [live_region] - {boolean}

  The maximum number of items in the cart to display. If the number of items in the cart is greater than this limit, the
  count will be displayed as "99+".
{%- enddoc -%}

<div
  ref="cartBubble"
  class="cart-bubble{% if cart == empty %} visually-hidden{% endif %}"
  {% assign item_count = cart.item_count %}
  {% if item_count <= 99 %}
    data-maintain-ratio
  {% endif %}
>
  <span class="cart-bubble__background"></span>
  <span
    ref="cartBubbleText"
    class="cart-bubble__text"
    {% if live_region %}
      role="status"
    {% endif %}
  >
    <span class="visually-hidden">
      {{- 'accessibility.cart_count' | t -}}
      : {{ cart.item_count }}
    </span>
    <span
      class="cart-bubble__text-count{% if cart == empty %} hidden{% endif %}"
      ref="cartBubbleCount"
      aria-hidden="true"
      data-testid="cart-bubble"
    >
      {%- if limit == blank or cart.item_count < limit %}
        {{- cart.item_count -}}
      {%- endif -%}
    </span>
  </span>
</div>
