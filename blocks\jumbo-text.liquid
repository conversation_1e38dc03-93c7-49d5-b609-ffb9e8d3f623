{% # import schema from '../schemas/blocks/jumbo-text' %}

{% render 'jumbo-text' %}

{% schema %}
{
  "name": "t:names.jumbo_text",
  "tag": null,
  "settings": [
    {
      "id": "text",
      "type": "textarea",
      "label": "t:settings.text",
      "default": "t:text_defaults.be_bold"
    },
    {
      "type": "select",
      "id": "font",
      "label": "t:settings.font",
      "options": [
        {
          "value": "body",
          "label": "t:options.body"
        },
        {
          "value": "subheading",
          "label": "t:options.subheading"
        },
        {
          "value": "heading",
          "label": "t:options.heading"
        },
        {
          "value": "accent",
          "label": "t:options.accent"
        }
      ],
      "default": "heading"
    },
    {
      "type": "text_alignment",
      "id": "alignment",
      "label": "t:settings.alignment",
      "default": "left"
    },
    {
      "type": "select",
      "id": "line_height",
      "label": "t:settings.line_height",
      "options": [
        {
          "value": "0.8",
          "label": "t:options.tight"
        },
        {
          "value": "1",
          "label": "t:options.normal"
        },
        {
          "value": "1.2",
          "label": "t:options.loose"
        }
      ],
      "default": "0.8"
    },
    {
      "type": "select",
      "id": "letter_spacing",
      "label": "t:settings.letter_spacing",
      "options": [
        {
          "value": "-0.03em",
          "label": "t:options.tight"
        },
        {
          "value": "normal",
          "label": "t:options.normal"
        },
        {
          "value": "0.03em",
          "label": "t:options.loose"
        }
      ],
      "default": "-0.03em"
    },
    {
      "type": "select",
      "id": "case",
      "label": "t:settings.case",
      "options": [
        {
          "value": "none",
          "label": "t:options.default"
        },
        {
          "value": "uppercase",
          "label": "t:options.uppercase"
        }
      ],
      "default": "none"
    },
    {
      "type": "select",
      "id": "text_effect",
      "label": "t:settings.effects",
      "options": [
        {
          "value": "none",
          "label": "t:options.none"
        },
        {
          "value": "blur",
          "label": "t:options.blur"
        },
        {
          "value": "reveal",
          "label": "t:options.reveal"
        }
      ],
      "default": "none"
    },
    {
      "type": "checkbox",
      "id": "animation_repeat",
      "label": "t:settings.animation_repeat",
      "default": false,
      "visible_if": "{{ block.settings.text_effect == \"blur\" or block.settings.text_effect == \"reveal\" }}"
    }
  ],
  "presets": [
    {
      "name": "t:names.jumbo_text",
      "category": "t:categories.decorative"
    }
  ]
}
{% endschema %}
