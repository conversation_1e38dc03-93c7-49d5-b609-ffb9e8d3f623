/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin language editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "names": {
    "404": "404",
    "borders": "Bordures",
    "collapsible_row": "Rangée réductible",
    "custom_section": "Section personnalisée",
    "icon": "Icône",
    "logo_and_favicon": "Logo et favicon",
    "product_buy_buttons": "Boutons d’achat",
    "product_description": "Description",
    "product_price": "Prix",
    "slideshow": "Diaporama",
    "typography": "Typographie",
    "video": "Vidéo",
    "colors": "Couleurs",
    "overlapping_blocks": "Chevauchement de blocs",
    "product_variant_picker": "Sélecteur de variante",
    "slideshow_controls": "Contrôles du diaporama",
    "size": "Taille",
    "spacing": "Espacement",
    "product_recommendations": "Produits recommandés",
    "product_media": "Média (produit)",
    "featured_collection": "Collection en vedette",
    "add_to_cart": "Ajouter au panier",
    "email_signup": "Inscription à la liste de diffusion",
    "submit_button": "Bouton Soumettre",
    "grid_layout_selector": "Sélecteur de mise en page en grille",
    "image": "Image",
    "list_items": "Articles de la liste",
    "facets": "Facettes",
    "variants": "Variantes",
    "styles": "Styles",
    "product_cards": "Cartes de produit",
    "primary_button": "Bouton primaire",
    "secondary_button": "Bouton secondaire",
    "popovers": "Popovers",
    "buttons": "Boutons",
    "inputs": "Entrées",
    "marquee": "Marquee",
    "alternating_content_rows": "Rangées alternées",
    "pull_quote": "Citation originale",
    "contact_form": "Formulaire de contact",
    "featured_product": "Produit en vedette",
    "icons_with_text": "Icônes avec texte",
    "jumbo_text": "Texte géant",
    "accelerated_checkout": "Paiement accéléré",
    "accordion": "Accordéon",
    "accordion_row": "Rangées d’accordéon",
    "animations": "Animations",
    "announcement": "Annonce",
    "announcement_bar": "Barre d’annonces",
    "badges": "Badges",
    "button": "Bouton",
    "cart": "Panier",
    "cart_items": "Articles dans le panier",
    "cart_products": "Produits dans le panier",
    "cart_title": "Panier",
    "collection": "Collection",
    "collection_card": "Carte de collection",
    "collection_columns": "Colonnes de collection",
    "collection_container": "Collection",
    "collection_description": "Description de la collection",
    "collection_image": "Image de la collection",
    "collection_info": "Informations sur la collection",
    "collection_list": "Liste des collections",
    "collections": "Collections",
    "content": "Contenu",
    "content_grid": "Grille de contenu",
    "details": "Détails",
    "divider": "Séparateur",
    "filters": "Filtrage et tri",
    "follow_on_shop": "Suivre sur Shop",
    "footer": "Pied de page",
    "footer_utilities": "Options du pied de page",
    "group": "Groupe",
    "header": "En-tête",
    "heading": "Titre",
    "icons": "Icônes",
    "image_with_text": "Image avec texte",
    "input": "Entrée",
    "logo": "Logo",
    "magazine_grid": "Grille de magazine",
    "media": "Support multimédia",
    "menu": "Menu",
    "mobile_layout": "Mise en page sur mobile",
    "payment_icons": "Icônes de paiement",
    "popup_link": "Lien ouvrant une pop-up",
    "predictive_search": "Rechercher le popover",
    "predictive_search_empty": "Recherche prédictive sans résultats",
    "price": "Prix",
    "product": "Produit",
    "product_card": "Carte de produit",
    "product_card_media": "Support multimédia",
    "product_card_rendering": "Présentation de la carte de produit",
    "product_grid": "Grille",
    "product_grid_main": "Grille de produit",
    "product_image": "Image de produit",
    "product_information": "Informations sur le produit",
    "product_review_stars": "Étoiles de notation",
    "quantity": "Quantité",
    "row": "Rangée",
    "search": "Rechercher",
    "section": "Section",
    "selected_variants": "Variantes sélectionnées",
    "shop_the_look": "Acheter le look",
    "slide": "Diapositive",
    "social_media_links": "Icônes de médias sociaux",
    "steps": "Étapes",
    "summary": "Résumé",
    "swatches": "Échantillons",
    "testimonials": "Témoignages",
    "text": "Texte",
    "title": "Titre",
    "utilities": "Utilitaires",
    "search_input": "Champ de recherche",
    "search_results": "Résultats de la recherche",
    "read_only": "Lecture seule",
    "collections_bento": "Liste de collections : Bento",
    "faq_section": "FAQ",
    "hero": "Héros",
    "product_list": "Liste de produits",
    "spacer": "Espacement",
    "video_section": "Vidéo",
    "custom_liquid": "Code Liquid personnalisé",
    "blog": "Blog",
    "blog_post": "Article de blog",
    "blog_posts": "Articles de blog",
    "caption": "Légende",
    "collection_card_image": "Image",
    "collection_title": "Titre de la collection",
    "collection_links": "Liens des collections",
    "collection_links_spotlight": "Liens de collections : Spotlight",
    "collection_links_text": "Liens de collections : Texte",
    "collections_carousel": "Liste de collections : Carrousel",
    "collections_editorial": "Liste de collections : éditorial",
    "collections_grid": "Liste de collections : Grille",
    "copyright": "Droits d’auteur",
    "count": "Quantité",
    "divider_section": "Séparateur",
    "drawers": "Tiroirs",
    "editorial": "Éditorial",
    "editorial_jumbo_text": "Éditorial : Texte géant",
    "hero_marquee": "Héros : Marquee",
    "input_fields": "Champs de saisie",
    "local_pickup": "Retrait en magasin",
    "marquee_section": "Marquee",
    "media_with_text": "Support multimédia avec texte",
    "page": "Page",
    "page_content": "Contenu",
    "page_layout": "Mise en page de la page",
    "policy_list": "Liens vers les politiques",
    "prices": "Prix",
    "product_list_button": "Bouton Afficher tout",
    "products_carousel": "Liste de produits : Carrousel",
    "products_editorial": "Liste de produits : éditorial",
    "products_grid": "Liste de produits : Grille",
    "social_link": "Lien de réseau social",
    "split_showcase": "Diviser la présentation",
    "variant_pickers": "Sélecteurs de variante",
    "view_all_button": "Voir tout",
    "pills": "Boutons pilule",
    "product_title": "Titre de produit",
    "large_logo": "Grand logo",
    "product_inventory": "Stock de produits",
    "description": "Description"
  },
  "settings": {
    "autoplay": "Jeu automatique",
    "background": "Arrière‑plan",
    "border_radius": "Rayon de coin",
    "border_width": "Épaisseur de la bordure",
    "borders": "Bordures",
    "bottom_padding": "Marge inférieure",
    "color": "Couleur",
    "content_direction": "Direction du contenu",
    "content_position": "Position du contenu",
    "cover_image_size": "Taille de l’image de couverture",
    "cover_image": "Image de couverture",
    "custom_width": "Largeur personnalisée",
    "enable_video_looping": "Lecture en boucle des vidéos",
    "favicon": "Favicon",
    "heading": "Titre",
    "icon": "Icône",
    "image_icon": "Icône de l’image",
    "make_section_full_width": "Rendre la section pleine largeur",
    "overlay_opacity": "Opacité de la superposition",
    "padding": "Marge intérieure",
    "product": "Produit",
    "text": "Texte",
    "top_padding": "Marge supérieure",
    "video": "Vidéo",
    "video_alt_text": "Texte alternatif",
    "video_loop": "Mettre en loop la vidéo",
    "video_position": "Position de la vidéo",
    "width": "Largeur",
    "alignment": "Alignement",
    "button": "Bouton",
    "colors": "Couleurs",
    "content_alignment": "Alignement du contenu",
    "custom_minimum_height": "Hauteur minimale personnalisée",
    "font_family": "Famille de polices",
    "gap": "Écart",
    "geometric_translate_y": "Translation géométrique Y",
    "image": "Image",
    "image_opacity": "Opacité de l’image",
    "image_position": "Position de l’image",
    "image_ratio": "Rapport d’aspect de l’image",
    "label": "Étiquette",
    "line_height": "Hauteur de la ligne",
    "link": "Lien",
    "layout_gap": "Écart dans la mise en page",
    "minimum_height": "Hauteur minimale",
    "opacity": "Opacité",
    "primary_color": "Liens",
    "section_width": "Largeur de la section",
    "size": "Taille",
    "slide_spacing": "Écart entre les diapositives",
    "slide_width": "Largeur de la diapositive",
    "slideshow_fullwidth": "Diapositives en pleine largeur",
    "style": "Style",
    "text_case": "Cas",
    "z_index": "Z-index",
    "limit_content_width": "Largeur limite du contenu",
    "color_scheme": "Nuancier de couleurs",
    "inherit_color_scheme": "Hériter du nuancier de couleurs",
    "product_count": "Nombre de produits",
    "product_type": "Type de produit",
    "content_width": "Largeur du contenu",
    "collection": "Collection",
    "enable_sticky_content": "Contenu fixe sur ordinateur",
    "error_color": "Erreur",
    "success_color": "Opération réussie",
    "primary_font": "Police principale",
    "secondary_font": "Police secondaire",
    "tertiary_font": "Police tertiaire",
    "columns": "Colonnes",
    "items_to_show": "Articles à afficher",
    "layout": "Mise en page",
    "layout_type": "Type",
    "show_grid_layout_selector": "Afficher le sélecteur de mise en page en grille",
    "view_more_show": "Afficher le bouton Tout afficher",
    "image_gap": "Écart d’image",
    "width_desktop": "Largeur d’écran de bureau",
    "width_mobile": "Largeur d’écran d’appareil mobile",
    "border_style": "Style de bordure",
    "height": "Hauteur",
    "thickness": "Épaisseur",
    "stroke": "Trait",
    "filter_style": "Style de filtre",
    "swatches": "Échantillons",
    "quick_add_colors": "Ajout de couleur rapide",
    "divider_color": "Séparateur",
    "border_opacity": "Opacité de la bordure",
    "hover_background": "Arrière‑plan de survol",
    "hover_borders": "Bordures de survol",
    "hover_text": "Texte de survol",
    "primary_hover_color": "Liens de survol",
    "primary_button_text": "Texte du bouton principal",
    "primary_button_background": "Arrière‑plan du bouton principal",
    "primary_button_border": "Bordure du bouton principal",
    "secondary_button_text": "Texte du bouton secondaire",
    "secondary_button_background": "Arrière‑plan du bouton secondaire",
    "secondary_button_border": "Bordure du bouton secondaire",
    "shadow_color": "Ombre",
    "video_autoplay": "Lecture automatique",
    "video_cover_image": "Image de couverture",
    "video_external_url": "URL",
    "video_source": "Source",
    "first_row_media_position": "Position des supports multimédias de la première rangée",
    "shadow_opacity": "Opacité de l’ombre",
    "show_filter_label": "Étiquettes de texte pour les filtres appliqués",
    "show_swatch_label": "Étiquettes de texte pour les échantillons",
    "accordion": "Accordéon",
    "aspect_ratio": "Format d’image",
    "auto_rotate_announcements": "Rotation automatique des annonces",
    "auto_rotate_slides": "Rotation automatique des diapositives",
    "badge_corner_radius": "Rayon de coin",
    "badge_position": "Position sur les cartes",
    "badge_sale_color_scheme": "Vente",
    "badge_sold_out_color_scheme": "Épuisé",
    "behavior": "Comportement",
    "blur": "Ombre floue",
    "border": "Bordure",
    "bottom": "En bas",
    "card_image_height": "Hauteur de l’image de produit",
    "carousel_on_mobile": "Carrousel sur appareil mobile",
    "cart_count": "Quantité dans le panier",
    "cart_items": "Articles dans le panier",
    "cart_related_products": "Produits associés",
    "cart_title": "Panier",
    "cart_total": "Total du panier",
    "cart_type": "Type",
    "case": "Cas",
    "checkout_buttons": "Boutons de paiement accéléré",
    "collection_list": "Collections",
    "collection_templates": "Modèles de collection",
    "content": "Contenu",
    "corner_radius": "Rayon de coin",
    "country_region": "Pays/région",
    "currency_code": "Code de devise",
    "custom_height": "Hauteur personnalisée",
    "desktop_height": "Hauteur du bureau",
    "direction": "Direction",
    "display": "Afficher",
    "divider_thickness": "Épaisseur du séparateur",
    "divider": "Séparateur",
    "dividers": "Séparateurs",
    "drop_shadow": "Ombre portée",
    "empty_state_collection_info": "Affiché avant la saisie d’une recherche",
    "empty_state_collection": "Collection vide",
    "enable_filtering": "Filtres",
    "enable_grid_density": "Contrôle de la mise en page de grille",
    "enable_sorting": "Tri",
    "enable_zoom": "Activer le zoom",
    "equal_columns": "Colonnes uniformes",
    "expand_first_group": "Agrandir le premier groupe",
    "extend_media_to_screen_edge": "Afficher le support multimédia en plein écran",
    "extend_summary": "Afficher en plein écran",
    "extra_large": "Très grand",
    "extra_small": "Très petit",
    "flag": "Marqueur",
    "font_price": "Police du prix",
    "font_weight": "Épaisseur de la police",
    "font": "Police",
    "full_width_first_image": "Image principale en pleine largeur",
    "full_width_on_mobile": "Pleine largeur sur mobile",
    "heading_preset": "Réglage prédéfini pour le titre",
    "hide_unselected_variant_media": "Masquer le support multimédia non sélectionné",
    "horizontal_gap": "Écart horizontal",
    "horizontal_offset": "Décalage horizontal de l’ombre",
    "hover_behavior": "Comportement au survol",
    "icon_background": "Arrière-plan de l’icône",
    "icons": "Icônes",
    "image_border_radius": "Rayon des coins de l’image",
    "installments": "Versements",
    "integrated_button": "Bouton intégré",
    "language_selector": "Sélecteur de langue",
    "large": "Grand",
    "left_padding": "Marge de gauche",
    "left": "Gauche",
    "letter_spacing": "Espacement des lettres",
    "limit_media_to_screen_height": "Limiter à la hauteur de l’écran",
    "limit_product_details_width": "Limiter la largeur des détails de produit",
    "link_preset": "Réglage prédéfini pour le lien",
    "links": "Liens",
    "logo": "Logo",
    "loop": "Boucle",
    "make_details_sticky_desktop": "Fixe sur le bureau",
    "max_width": "Largeur max.",
    "media_height": "Hauteur du support multimédia",
    "media_overlay": "Superposition du support multimédia",
    "media_position": "Position du support multimédia",
    "media_type": "Type de support multimédia",
    "media_width": "Largeur du support multimédia",
    "menu": "Menu",
    "mobile_columns": "Colonnes sur appareil mobile",
    "mobile_height": "Hauteur sur appareil mobile",
    "mobile_logo_image": "Logo sur mobile",
    "mobile_quick_add": "Ajout rapide sur appareil mobile",
    "motion_direction": "Direction du mouvement",
    "motion": "Mouvement",
    "movement_direction": "Direction du déplacement",
    "navigation_bar_color_scheme": "Schéma de couleurs de la barre de navigation",
    "navigation_bar": "Barre de navigation",
    "navigation": "Navigation",
    "open_new_tab": "Ouvrir le lien dans un nouvel onglet",
    "overlay_color": "Couleur de superposition",
    "overlay": "Superposition",
    "padding_bottom": "Marge inférieure",
    "padding_horizontal": "Marge horizontale",
    "padding_top": "Marge supérieure",
    "page_width": "Largeur de la page",
    "pagination": "Pagination",
    "placement": "Placement",
    "position": "Position",
    "preset": "Réglage prédéfini",
    "product_cards": "Cartes de produits",
    "product_pages": "Pages de produits",
    "product_templates": "Modèles de produits",
    "products": "Produits",
    "quick_add": "Ajout rapide",
    "ratio": "Rapport",
    "regular": "Normal",
    "review_count": "Nombre d’avis",
    "right": "Droite",
    "row_height": "Hauteur de la rangée",
    "row": "Rangée",
    "seller_note": "Ajouter un message pour le vendeur",
    "shape": "Forme",
    "show_as_accordion": "Afficher sous forme d’accordéon sur appareil mobile",
    "show_sale_price_first": "Afficher d’abord le prix avant réduction",
    "show_tax_info": "Informations fiscales",
    "show": "Afficher",
    "small": "Petit",
    "speed": "Vitesse",
    "statement": "Relevé",
    "sticky_header": "En-tête fixe",
    "text_hierarchy": "Hiérarchie du texte",
    "text_presets": "Réglages de texte prédéfinis",
    "title": "Titre",
    "top": "Haut",
    "type": "Type",
    "type_preset": "Réglage de texte prédéfini",
    "underline_thickness": "Largeur du soulignement",
    "variant_images": "Images de variantes",
    "vendor": "Fournisseur",
    "vertical_gap": "Écart vertical",
    "vertical_offset": "Décalage vertical de l’ombre",
    "vertical_on_mobile": "Vertical sur appareil mobile",
    "view_all_as_last_card": "« Afficher tout » comme dernière carte",
    "weight": "Poids",
    "wrap": "Renvoi à la ligne",
    "read_only": "Lecture seule",
    "always_stack_buttons": "Toujours empiler les boutons",
    "background_color": "Couleur d’arrière-plan",
    "custom_mobile_size": "Taille mobile personnalisée",
    "custom_mobile_width": "Largeur mobile personnalisée",
    "fixed_height": "Hauteur en pixels",
    "fixed_width": "Largeur en pixels",
    "gradient_direction": "Direction du dégradé",
    "hide_padding": "Masquer l’espacement",
    "logo_font": "Police du logo",
    "overlay_style": "Style de superposition",
    "percent_height": "Hauteur en pourcentage",
    "percent_size_mobile": "Taille en pourcentage",
    "percent_size": "Taille en pourcentage",
    "percent_width": "Largeur en pourcentage",
    "pixel_size_mobile": "Taille en pixels",
    "pixel_size": "Taille en pixels",
    "size_mobile": "Taille mobile",
    "transparent_background": "Arrière-plan transparent",
    "unit": "Unité",
    "account": "Compte",
    "align_baseline": "Aligner la ligne de base du texte",
    "add_discount_code": "Autoriser les réductions dans le panier",
    "background_overlay": "Superposition d’arrière-plan",
    "background_media": "Support multimédia en arrière-plan",
    "border_thickness": "Épaisseur de la bordure",
    "bottom_row": "Rangée inférieure",
    "button_text_case": "Casse du texte",
    "button_text_weight": "Épaisseur du texte",
    "card_size": "Taille de carte",
    "auto_open_cart_drawer": "L’option « Ajouter au panier » ouvre automatiquement le panier",
    "collection_count": "Nombre de collections",
    "custom_liquid": "Code Liquid",
    "default": "Par défaut",
    "default_logo": "Logo par défaut",
    "divider_width": "Largeur du séparateur",
    "headings": "Titres",
    "hide_logo_on_home_page": "Masquer le logo sur la page d’accueil",
    "horizontal_padding": "Espacement horizontal",
    "inverse": "Inverse",
    "inverse_logo": "Logo inverse",
    "layout_style": "Style",
    "length": "Longueur",
    "mobile_card_size": "Taille de carte mobile",
    "mobile_pagination": "Pagination mobile",
    "open_row_by_default": "Ouvrir la rangée par défaut",
    "page": "Page",
    "page_transition_enabled": "Transition de la page",
    "right_padding": "Espacement à droite",
    "search": "Rechercher",
    "search_icon": "Icône de recherche",
    "search_position": "Position",
    "search_row": "Rangée",
    "show_author": "Auteur",
    "show_alignment": "Afficher l’alignement",
    "show_count": "Afficher la quantité",
    "show_date": "Date",
    "show_pickup_availability": "Afficher la disponibilité de retrait",
    "show_search": "Afficher la recherche",
    "use_inverse_logo": "Utiliser le logo inverse",
    "vertical_padding": "Espacement vertical",
    "visibility": "Visibilité",
    "product_corner_radius": "Rayon des coins du produit",
    "card_corner_radius": "Rayon des coins de la carte",
    "alignment_mobile": "Alignement sur mobile",
    "animation_repeat": "Répéter l’animation",
    "blurred_reflection": "Réflexion floutée",
    "card_hover_effect": "Effet de survol des cartes",
    "collection_title_case": "Casse du titre de la collection",
    "effects": "Effets",
    "inventory_threshold": "Seuil de stock faible",
    "product_and_card_title_case": "Casse du titre de produit et de la carte",
    "product_title_case": "Casse du titre de produit",
    "reflection_opacity": "Opacité de la réflexion",
    "show_inventory_quantity": "Afficher la quantité de stock faible",
    "text_label_case": "Casse de l’étiquette de texte",
    "transition_to_main_product": "Transition de carte de produit à page de produit",
    "media": "Support multimédia",
    "product_card_carousel": "Afficher le carrousel",
    "show_second_image_on_hover": "Afficher la deuxième image au passage de la souris"
  },
  "options": {
    "adapt_to_image": "Adapter à l’image",
    "apple": "Pomme",
    "arrow": "Flèche",
    "banana": "Banane",
    "bottle": "Bouteille",
    "box": "Colis",
    "buttons": "Boutons",
    "carrot": "Carotte",
    "center": "Centre",
    "chat_bubble": "Bulle de chat",
    "clipboard": "Presse-papiers",
    "contain": "Contenir",
    "counter": "Compteur",
    "cover": "Couverture",
    "custom": "Personnalisé",
    "dairy_free": "Sans produits laitiers",
    "dairy": "Produits laitiers",
    "dropdowns": "Menus déroulants",
    "dots": "Points",
    "dryer": "Sèche-linge",
    "end": "Fin",
    "eye": "Œil",
    "facebook": "Facebook",
    "fire": "Feu",
    "gluten_free": "Sans gluten",
    "heart": "Cœur",
    "horizontal": "Horizontale",
    "instagram": "Instagram",
    "iron": "Fer",
    "large": "Grand",
    "leaf": "Feuille",
    "leather": "Cuir",
    "lightning_bolt": "Foudre",
    "lipstick": "Rouge à lèvres",
    "lock": "Cadenas",
    "map_pin": "Épingle de carte",
    "medium": "Moyen",
    "none": "Aucun",
    "numbers": "Numéros",
    "nut_free": "Sans noix",
    "pants": "Pantalons",
    "paw_print": "Empreinte de patte",
    "pepper": "Poivre",
    "perfume": "Parfum",
    "pinterest": "Pinterest",
    "plane": "Avion",
    "plant": "Plante",
    "price_tag": "Étiquette de prix",
    "question_mark": "Point d’interrogation",
    "recycle": "Recyclage",
    "return": "Retour",
    "ruler": "Règle",
    "serving_dish": "Plat de service",
    "shirt": "Chemise",
    "shoe": "Chaussure",
    "silhouette": "Silhouette",
    "small": "Petit",
    "snapchat": "Snapchat",
    "snowflake": "Flocon de neige",
    "star": "Étoile",
    "start": "Début",
    "stopwatch": "Chronomètre",
    "tiktok": "TikTok",
    "truck": "Camion",
    "tumblr": "Tumblr",
    "twitter": "X (Twitter)",
    "vertical": "Verticale",
    "vimeo": "Vimeo",
    "washing": "Lavage",
    "auto": "Auto",
    "default": "Par défaut",
    "fill": "Remplir",
    "fit": "Ajuster",
    "full": "Complet",
    "full_and_page": "Arrière-plan complet, contenu de la largeur de la page",
    "heading": "Titre",
    "landscape": "Paysage",
    "lg": "L",
    "link": "Lien",
    "lowercase": "minuscule",
    "m": "M",
    "outline": "Contour",
    "page": "Page",
    "portrait": "Portrait",
    "s": "S",
    "sentence": "Phrase",
    "solid": "Uni",
    "space_between": "Espace entre",
    "square": "Carré",
    "uppercase": "Majuscule",
    "circle": "Cercle",
    "swatches": "Échantillons",
    "full_and_page_offset_left": "Arrière-plan complet, contenu de la largeur de la page, décalage à gauche",
    "full_and_page_offset_right": "Arrière-plan complet, contenu de la largeur de la page, décalage à droite",
    "offset_left": "Décalage à gauche",
    "offset_right": "Décalage à droite",
    "page_center_aligned": "Page, centrée",
    "page_left_aligned": "Page, alignée à gauche",
    "page_right_aligned": "Page, alignée à droite",
    "button": "Bouton",
    "caption": "Légende",
    "h1": "Titre 1",
    "h2": "Titre 2",
    "h3": "Titre 3",
    "h4": "Titre 4",
    "h5": "Titre 5",
    "h6": "Titre 6",
    "paragraph": "Paragraphe",
    "primary": "Principal",
    "secondary": "Secondaire",
    "tertiary": "Tertiaire",
    "chevron_left": "Chevron gauche",
    "chevron_right": "Chevron droite",
    "diamond": "Losange",
    "grid": "Grille",
    "parallelogram": "Parallélogramme",
    "rounded": "Arrondis",
    "fit_content": "Ajuster",
    "pills": "Boutons pilule",
    "heavy": "Épais",
    "thin": "Fin",
    "drawer": "Tiroir",
    "preview": "Aperçu",
    "text": "Texte",
    "video_uploaded": "Téléchargée",
    "video_external_url": "URL externe",
    "aspect_ratio": "Proportions",
    "above_carousel": "Au-dessus du carrousel",
    "all": "Tous",
    "always": "Toujours",
    "arrows_large": "Grandes flèches",
    "arrows": "Flèches",
    "balance": "Solde",
    "bento": "Bento",
    "black": "Noir",
    "bluesky": "Bluesky",
    "body_large": "Corps (grand)",
    "body_regular": "Corps (normal)",
    "body_small": "Corps (petit)",
    "bold": "Gras",
    "bottom_left": "En bas à gauche",
    "bottom_right": "En bas à droite",
    "bottom": "En bas",
    "capitalize": "Mettre en majuscules",
    "caret": "Caret",
    "carousel": "Carrousel",
    "check_box": "Case à cocher",
    "chevron_large": "Gros chevrons",
    "chevron": "Chevron",
    "chevrons": "Chevrons",
    "classic": "Classique",
    "collection_images": "Images de la collection",
    "color": "Couleur",
    "complementary": "Complémentaire",
    "dissolve": "Fondu",
    "dotted": "Pointillé",
    "editorial": "Éditorial",
    "extra_large": "Très grand",
    "extra_small": "Très petit",
    "featured_collections": "Collections en vedette",
    "featured_products": "Produits en vedette",
    "font_primary": "Principal",
    "font_secondary": "Secondaire",
    "font_tertiary": "Tertiaire",
    "forward": "Avancer",
    "full_screen": "Plein écran",
    "heading_extra_large": "Titre (très grand)",
    "heading_extra_small": "Titre (très petit)",
    "heading_large": "Titre (grand)",
    "heading_regular": "Titre (normal)",
    "heading_small": "Titre (petit)",
    "icon": "Icône",
    "image": "Image",
    "input": "Entrée",
    "inside_carousel": "Dans le carrousel",
    "inverse_large": "Inversé en grande taille",
    "inverse": "Inverse",
    "large_arrows": "Grandes flèches",
    "large_chevrons": "Gros chevrons",
    "left": "Gauche",
    "light": "Clair",
    "linkedin": "LinkedIn",
    "loose": "Lâche",
    "media_first": "Support multimédia en premier",
    "media_second": "Support multimédia en second",
    "modal": "Fenêtre modale",
    "narrow": "Étroit",
    "never": "Jamais",
    "next_to_carousel": "À côté du carrousel",
    "normal": "Normal",
    "nowrap": "Pas de retour à la ligne",
    "off_media": "En dehors du support multimédia",
    "on_media": "Sur le support multimédia",
    "on_scroll_up": "Lors du défilement vers le haut",
    "one_half": "1/2",
    "one_number": "1",
    "one_third": "1/3",
    "pill": "Rectangle arrondi",
    "plus": "Plus",
    "pretty": "Optimisé",
    "price": "Prix",
    "primary_style": "Style principal",
    "rectangle": "Rectangle",
    "regular": "Normal",
    "related": "Associé",
    "reverse": "Inverser",
    "rich_text": "Texte enrichi",
    "right": "Droite",
    "secondary_style": "Style secondaire",
    "semibold": "Semi-gras",
    "shaded": "Ombré",
    "show_second_image": "Afficher la deuxième image",
    "single": "Unique",
    "slide_left": "Faire glisser vers la gauche",
    "slide_up": "Glisser vers le haut",
    "spotify": "Spotify",
    "stack": "Pile",
    "text_only": "Texte uniquement",
    "threads": "Threads",
    "thumbnails": "Miniatures",
    "tight": "Serré",
    "top_left": "En haut à gauche",
    "top_right": "En haut à droite",
    "top": "Haut",
    "two_number": "2",
    "two_thirds": "2/3",
    "underline": "Souligner",
    "video": "Vidéo",
    "wide": "Large",
    "youtube": "YouTube",
    "down": "Bas",
    "fixed": "Fixe",
    "gradient": "Dégradé",
    "percent": "Pourcentage",
    "pixel": "Pixel",
    "up": "Haut",
    "compact": "Compacte",
    "standard": "Standard",
    "accent": "Accent",
    "below_image": "Sous l’image",
    "body": "Corps",
    "button_primary": "Bouton primaire",
    "button_secondary": "Bouton secondaire",
    "crop_to_fit": "Recadrer pour adapter",
    "hidden": "Masqué",
    "hint": "Astuce",
    "maintain_aspect_ratio": "Conserver les proportions",
    "off": "Désactivé",
    "on_image": "Sur l’image",
    "social_bluesky": "Social : Bluesky",
    "social_facebook": "Social : Facebook",
    "social_instagram": "Social : Instagram",
    "social_linkedin": "Social : LinkedIn",
    "social_pinterest": "Social : Pinterest",
    "social_snapchat": "Social : Snapchat",
    "social_spotify": "Social : Spotify",
    "social_threads": "Social : Threads",
    "social_tiktok": "Social : TikTok",
    "social_tumblr": "Social : Tumblr",
    "social_twitter": "Social : X (Twitter)",
    "social_whatsapp": "Social : WhatsApp",
    "social_vimeo": "Social : Vimeo",
    "social_youtube": "Social : YouTube",
    "spotlight": "Spotlight",
    "subheading": "Sous-titre",
    "blur": "Flou",
    "lift": "Élévation",
    "reveal": "Révéler",
    "scale": "Échelle",
    "subtle_zoom": "Zoom"
  },
  "content": {
    "background_video": "Vidéo d’arrière-plan",
    "describe_the_video_for": "Décrivez la vidéo pour les clients utilisant des lecteurs d’écran. [En savoir plus](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)",
    "width_is_automatically_optimized": "La largeur est automatiquement optimisée pour les appareils mobiles.",
    "advanced": "Avancé",
    "background_image": "Image de fond",
    "block_size": "Taille du bloc",
    "borders": "Bordures",
    "section_size": "Taille de la section",
    "slideshow_width": "Largeur de la diapositive",
    "typography": "Typographie",
    "complementary_products": "Les produits complémentaires doivent être configurés via l’application Search & Discovery. [Learn more ](https://help.shopify.com/manual/online-store/search-and-discovery)",
    "mobile_column_optimization": "Les colonnes s’adapteront automatiquement à l’affichage des appareils mobiles",
    "content_width": "La largeur du contenu s’applique uniquement si la largeur de la section est définie sur pleine largeur.",
    "adjustments_affect_all_content": "S’applique à l’ensemble du contenu de ce bloc",
    "responsive_font_sizes": "Les tailles s’ajustent automatiquement à tous les formats d’écran",
    "buttons": "Boutons",
    "swatches": "Échantillons",
    "variant_settings": "Paramètres des variantes",
    "background": "Arrière‑plan",
    "appearance": "Apparence",
    "arrows": "Flèches",
    "body_size": "Taille du corps",
    "bottom_row_appearance": "Apparence de la rangée inférieure",
    "carousel_navigation": "Navigation dans le carrousel",
    "carousel_pagination": "Pagination du carrousel",
    "copyright": "Droits d’auteur",
    "edit_logo_in_theme_settings": "Modifiez le logo dans [theme settings ](/editor?context=theme&category=logo%20and%20favicon)",
    "edit_price_in_theme_settings": "Modifiez le formatage des prix dans [theme settings ](/editor?context=theme&category=currency%20code)",
    "edit_variants_in_theme_settings": "Modifiez les variantes de style dans [theme settings ](/editor?context=theme&category=variants)",
    "email_signups_create_customer_profiles": "Ajout d’inscriptions [customer profiles ](https://help.shopify.com/manual/customers)",
    "follow_on_shop_eligiblity": "Pour afficher le bouton, le canal Shop doit être installé et Shop Pay activé. [Learn more ](https://help.shopify.com/en/manual/online-store/themes/customizing-themes/add-shop-buttons)",
    "fonts": "Polices",
    "grid": "Grille",
    "heading_size": "Taille du titre",
    "image": "Image",
    "input": "Entrée",
    "layout": "Mise en page",
    "link": "Lien",
    "link_padding": "Marge du lien",
    "localization": "Localisation",
    "logo": "Logo",
    "margin": "Marge",
    "media": "Support multimédia",
    "media_1": "Support multimédia 1",
    "media_2": "Support multimédia 2",
    "menu": "Menu",
    "mobile_layout": "Mise en page sur mobile",
    "padding": "Marge",
    "padding_desktop": "Marge du bureau",
    "paragraph": "Paragraphe",
    "policies": "Politiques",
    "popup": "Pop-up",
    "search": "Rechercher",
    "size": "Taille",
    "social_media": "Médias sociaux",
    "submit_button": "Bouton Soumettre",
    "text_presets": "Réglages de texte prédéfinis",
    "transparent_background": "Arrière-plan transparent",
    "typography_primary": "Typographie principale",
    "typography_secondary": "Typographie secondaire",
    "typography_tertiary": "Typographie tertiaire",
    "mobile_size": "Taille mobile",
    "cards_layout": "Mise en page des cartes",
    "mobile_width": "Largeur mobile",
    "section_layout": "Mise en page de la section",
    "width": "Largeur",
    "visible_if_collection_has_more_products": "Visible si la collection comprend plus de produits que ceux affichés",
    "carousel": "Carrousel",
    "colors": "Couleurs",
    "collection_page": "Page de collection",
    "copyright_info": "Découvrez comment [modifier votre déclaration de droits d’auteur](https://help.shopify.com/manual/online-store/themes/customizing-themes/remove-powered-by-shopify-message)",
    "customer_account": "Compte client",
    "edit_empty_state_collection_in_theme_settings": "Modifier la collection par défaut dans les [paramètres de thèmes ](/editor?context=theme&category=search)",
    "grid_layout": "Mise en page en grille",
    "home_page": "Page d’accueil",
    "images": "Images",
    "inverse_logo_info": "Utilisé lorsque l’arrière-plan d’en-tête transparent est défini sur Inverse",
    "manage_customer_accounts": "[Gérer la visibilité](/admin/settings/customer_accounts) dans les paramètres des comptes clients. Les anciens comptes ne sont pas pris en charge.",
    "manage_policies": "[Gérer les politiques](/admin/settings/legal)",
    "product_page": "Page de produit",
    "text": "Texte",
    "thumbnails": "Miniatures",
    "visibility": "Visibilité",
    "app_required_for_ratings": "Une application est requise pour les évaluations de produits. [En savoir plus](https://help.shopify.com/manual/apps)",
    "icon": "Icône",
    "manage_store_name": "[Gérer le nom de la boutique :](/admin/settings/general?edit=storeName)",
    "resource_reference_collection_card": "Affiche la collection de la section parente",
    "resource_reference_collection_card_image": "Affiche l'image de la collection parente",
    "resource_reference_collection_title": "Affiche le titre de la collection parente",
    "resource_reference_product": "Se connecte automatiquement au produit parent",
    "resource_reference_product_card": "Affiche le produit de la section parente",
    "resource_reference_product_inventory": "Affiche l'inventaire du produit parent",
    "resource_reference_product_price": "Affiche le prix du produit parent",
    "resource_reference_product_recommendations": "Affiche les recommandations basées sur le produit parent",
    "resource_reference_product_review": "Affiche les avis du produit parent",
    "resource_reference_product_swatches": "Affiche les échantillons du produit parent",
    "resource_reference_product_title": "Affiche le titre du produit parent",
    "resource_reference_product_variant_picker": "Affiche les variantes du produit parent",
    "resource_reference_product_media": "Affiche les supports multimédias du produit parent"
  },
  "html_defaults": {
    "share_information_about_your": "<p>Partagez des informations sur votre marque. Décrivez un produit, partagez des annonces ou souhaitez la bienvenue à vos clients dans votre boutique.</p>"
  },
  "text_defaults": {
    "collapsible_row": "Rangée réductible",
    "button_label": "Acheter maintenant",
    "heading": "Titre",
    "email_signup_button_label": "S’abonner",
    "be_bold": "Osez.",
    "accordion_heading": "Titre de l’accordéon",
    "contact_form_button_label": "Soumettre",
    "popup_link": "Lien ouvrant une pop-up",
    "sign_up": "S’inscrire",
    "welcome_to_our_store": "Bienvenue dans notre boutique",
    "shop_our_latest_arrivals": "Achetez nos dernières arrivées !"
  },
  "info": {
    "carousel_layout_on_mobile": "Le carrousel est utilisé sur mobile",
    "video_alt_text": "Décrivez la vidéo pour les utilisateurs de technologie d’assistance",
    "video_autoplay": "Le son des vidéos sera désactivé par défaut",
    "video_external": "Utilisez une URL YouTube ou Vimeo",
    "link_info": "Facultatif : permet de cliquer sur l’icône",
    "carousel_hover_behavior_not_supported": "Le survol du « carrousel » n’est pas pris en charge lorsque le type « Carrousel » est sélectionné au niveau de la section",
    "checkout_buttons": "Accélère le processus de paiement pour les acheteurs et peut optimiser le taux de conversion. [Learn more ](https://help.shopify.com/manual/online-store/dynamic-checkout)",
    "custom_heading": "Titre personnalisé",
    "edit_presets_in_theme_settings": "Modifiez les réglages prédéfinis dans [theme settings ](/editor?context=theme&category=typography)",
    "enable_filtering_info": "Personnalisez les filtres avec [Search & Discovery app](https://help.shopify.com/manual/online-store/search-and-discovery/filters)",
    "grid_layout_on_mobile": "La mise en page de grille est utilisée pour les appareils mobiles",
    "manage_countries_regions": "[Manage countries/regions](/admin/settings/markets) ",
    "manage_languages": "[Manage languages](/admin/settings/languages) ",
    "transparent_background": "Vérifiez chaque modèle utilisant un arrière-plan transparent pour s’assurer qu’il reste lisible",
    "logo_font": "S’applique uniquement lorsqu’un logo n’est pas sélectionné",
    "aspect_ratio_adjusted": "Ajusté dans certaines mises en page",
    "auto_open_cart_drawer": "Lorsque cette option est activée, le panier coulissant s’ouvre automatiquement lorsqu’un produit est ajouté.",
    "custom_liquid": "Ajoutez des extraits d’application ou d’autres éléments de code pour créer des personnalisations avancées.[En savoir plus](https://shopify.dev/docs/api/liquid)",
    "pills_usage": "Utilisée pour les filtres appliqués, les codes de réduction et les suggestions de recherche",
    "applies_on_image_only": "S’applique uniquement aux images",
    "hover_effects": "S’applique à des cartes de produits et de collections"
  },
  "categories": {
    "basic": "Basic",
    "collection": "Collection",
    "collection_list": "Liste des collections",
    "footer": "Pied de page",
    "forms": "Formulaires",
    "header": "En-tête",
    "layout": "Mise en page",
    "links": "Liens",
    "product": "Produit",
    "product_list": "Liste de produits",
    "banners": "Bannières",
    "collections": "Collections",
    "custom": "Personnalisées",
    "decorative": "Décoratives",
    "products": "Produits",
    "other_sections": "Autre",
    "storytelling": "Narration"
  }
}
